import { AttachmentReference } from "@braintrust/typespecs";
import { isArray, isObject } from "braintrust/util";
import { getBase64Parts, isBase64File } from "@braintrust/local/functions";
import { base64ToArrayBuffer } from "@braintrust/proxy/utils";
import { getLogger } from "./instrumentation/logger";
import { ALLOW_INVALID_BASE64 } from "./env";
import { BadRequestError } from "./util";

export function replacePayloadWithAttachmentsCallback(args: {
  data: Record<string, unknown>;
  replaceWithAttachment: (args: {
    data: ArrayBuffer;
    contentType: string;
    filename: string;
  }) => unknown;
  attachments: Record<string, AttachmentReference>;
}): Record<string, unknown> {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return replacePayloadWithAttachmentsCallbackInner(args) as Record<
    string,
    unknown
  >;
}

function isAnthropicBase64Object(
  data: unknown,
): data is { data: string; media_type: string; type: string } {
  return (
    isObject(data) &&
    typeof data.data === "string" &&
    typeof data.media_type === "string" &&
    data.type === "base64"
  );
}

function isOpenAIImageGenerationObject(
  data: unknown,
): data is { result: string; output_format: string; type: string } {
  return (
    isObject(data) &&
    typeof data.result === "string" &&
    typeof data.output_format === "string" &&
    data.type === "image_generation_call"
  );
}

function replacePayloadWithAttachmentsCallbackInner({
  data,
  replaceWithAttachment,
  attachments,
}: {
  data: unknown;
  replaceWithAttachment: (args: {
    data: ArrayBuffer;
    contentType: string;
    filename: string;
  }) => unknown;
  attachments: Record<string, AttachmentReference>;
}): unknown {
  if (isArray(data)) {
    return data.map((item) =>
      replacePayloadWithAttachmentsCallbackInner({
        data: item,
        replaceWithAttachment,
        attachments,
      }),
    );
  } else if (isObject(data)) {
    if (isAnthropicBase64Object(data)) {
      try {
        const arrayBuffer = base64ToArrayBuffer(data.data);
        const filename = `file.${data.media_type.split("/")[1]}`;
        return replaceWithAttachment({
          data: arrayBuffer,
          contentType: data.media_type,
          filename,
        });
      } catch (e) {
        if (ALLOW_INVALID_BASE64) {
          getLogger().warn(
            "Failed to parse Anthropic base64 attachment, returning original object",
            e,
          );
          return data;
        } else {
          throw new BadRequestError("Invalid base64 in request");
        }
      }
    }
    if (isOpenAIImageGenerationObject(data)) {
      try {
        const arrayBuffer = base64ToArrayBuffer(data.result);
        const contentType = `image/${data.output_format}`;
        const filename = `file.${data.output_format}`;
        return replaceWithAttachment({
          data: arrayBuffer,
          contentType,
          filename,
        });
      } catch (e) {
        if (ALLOW_INVALID_BASE64) {
          getLogger().warn(
            "Failed to parse OpenAI base64 attachment, returning original object",
            e,
          );
          return data;
        } else {
          throw new BadRequestError("Invalid base64 in request");
        }
      }
    }
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        replacePayloadWithAttachmentsCallbackInner({
          data: value,
          replaceWithAttachment,
          attachments,
        }),
      ]),
    );
  } else if (typeof data === "string") {
    if (attachments[data]) {
      return attachments[data];
    } else if (isBase64File(data, () => true)) {
      try {
        const { mimeType, data: arrayBuffer } = getBase64Parts(data);
        const filename = `file.${mimeType.split("/")[1]}`;
        return replaceWithAttachment({
          data: arrayBuffer,
          contentType: mimeType,
          filename,
        });
      } catch (e) {
        // NOTE: By default (unless this flag is set), we throw an error if we fail to parse invalid
        // base64. The reason for this is that base64 payloads can be very large and we want to avoid
        // causing downstream issues by permitting them. However, certain customers (like Replit) who
        // self hosts may want to allow this behavior.
        if (ALLOW_INVALID_BASE64) {
          getLogger().warn(
            "Failed to parse base64 attachment, returning original string",
            e,
          );
          return data;
        } else {
          throw new BadRequestError("Invalid base64 in request");
        }
      }
    } else {
      return data;
    }
  } else {
    return data;
  }
}
