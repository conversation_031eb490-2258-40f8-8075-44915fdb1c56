import { expect, test } from "vitest";
import { replacePayloadWithAttachmentsCallback } from "./replace-attachment";
import { AttachmentReference } from "@braintrust/typespecs";

test("replacePayloadWithAttachmentsCallback handles Anthropic base64 format", () => {
  const uploadedAttachments: Array<{
    data: ArrayBuffer;
    contentType: string;
    filename: string;
  }> = [];

  const input = [
    {
      content: [
        {
          source: {
            data: "JVBERi0xLjMKJcTl8uXrp/Og0MTGCjMgMCBvYmoKPDwgL0ZpbHRlciAvRmxhdGVEZWNvZGUgL0xlbmd0aCA=",
            media_type: "application/pdf",
            type: "base64",
          },
          type: "document",
        },
        {
          text: "What is in this document?",
          type: "text",
        },
      ],
      role: "user",
    },
  ];

  const result = replacePayloadWithAttachmentsCallback({
    data: input as unknown as Record<string, unknown>,
    replaceWithAttachment: (args) => {
      uploadedAttachments.push(args);
      return {
        type: "braintrust_attachment",
        key: "test-key",
        filename: args.filename,
        content_type: args.contentType,
      };
    },
    attachments: {},
  });

  expect(uploadedAttachments).toHaveLength(1);
  expect(uploadedAttachments[0].contentType).toBe("application/pdf");
  expect(uploadedAttachments[0].filename).toBe("file.pdf");
  expect(uploadedAttachments[0].data).toBeInstanceOf(ArrayBuffer);

  const resultArray = result as unknown as unknown[];
  const firstMessage = resultArray[0] as Record<string, unknown>;
  const content = firstMessage.content as unknown[];
  const documentContent = content[0] as Record<string, unknown>;
  const source = documentContent.source as AttachmentReference;

  expect(source.type).toBe("braintrust_attachment");
  expect(source.content_type).toBe("application/pdf");
  expect(source.filename).toBe("file.pdf");
});

test("replacePayloadWithAttachmentsCallback handles data URI format", () => {
  const uploadedAttachments: Array<{
    data: ArrayBuffer;
    contentType: string;
    filename: string;
  }> = [];

  const input = {
    image:
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
  };

  const result = replacePayloadWithAttachmentsCallback({
    data: input,
    replaceWithAttachment: (args) => {
      uploadedAttachments.push(args);
      return {
        type: "braintrust_attachment",
        key: "test-key-2",
        filename: args.filename,
        content_type: args.contentType,
      };
    },
    attachments: {},
  });

  expect(uploadedAttachments).toHaveLength(1);
  expect(uploadedAttachments[0].contentType).toBe("image/png");
  expect(uploadedAttachments[0].filename).toBe("file.png");

  const resultObj = result as Record<string, unknown>;
  const image = resultObj.image as AttachmentReference;

  expect(image.type).toBe("braintrust_attachment");
  expect(image.content_type).toBe("image/png");
});

test("replacePayloadWithAttachmentsCallback handles nested Anthropic format", () => {
  const uploadedAttachments: Array<{
    data: ArrayBuffer;
    contentType: string;
    filename: string;
  }> = [];

  const input = {
    messages: [
      {
        content: [
          {
            image: {
              data: "iVBORw0KGgo=",
              media_type: "image/jpeg",
              type: "base64",
            },
          },
        ],
      },
    ],
  };

  const result = replacePayloadWithAttachmentsCallback({
    data: input,
    replaceWithAttachment: (args) => {
      uploadedAttachments.push(args);
      return {
        type: "braintrust_attachment",
        key: "test-key-3",
        filename: args.filename,
        content_type: args.contentType,
      };
    },
    attachments: {},
  });

  expect(uploadedAttachments).toHaveLength(1);
  expect(uploadedAttachments[0].contentType).toBe("image/jpeg");
  expect(uploadedAttachments[0].filename).toBe("file.jpeg");

  const resultObj = result as Record<string, unknown>;
  const messages = resultObj.messages as unknown[];
  const message = messages[0] as Record<string, unknown>;
  const content = message.content as unknown[];
  const imageContent = content[0] as Record<string, unknown>;
  const image = imageContent.image as AttachmentReference;

  expect(image.type).toBe("braintrust_attachment");
  expect(image.content_type).toBe("image/jpeg");
});

test("replacePayloadWithAttachmentsCallback handles OpenAI image generation format", () => {
  const uploadedAttachments: Array<{
    data: ArrayBuffer;
    contentType: string;
    filename: string;
  }> = [];

  const input = [
    {
      background: "opaque",
      id: "ig_07d80078af2897c00068ddb4ba401c8195814bf23e109b29cc",
      output_format: "png",
      quality: "high",
      result:
        "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
      revised_prompt: "serene mountain landscape at sunset",
      size: "1536x1024",
      status: "completed",
      type: "image_generation_call",
    },
    {
      content: [
        {
          annotations: [],
          logprobs: [],
          text: "Here is the image of a serene mountain landscape at sunset.",
          type: "output_text",
        },
      ],
      id: "msg_07d80078af2897c00068ddb4f32d088195b88f4af1dcbab05b",
      role: "assistant",
      status: "completed",
      type: "message",
    },
  ];

  const result = replacePayloadWithAttachmentsCallback({
    data: input as unknown as Record<string, unknown>,
    replaceWithAttachment: (args) => {
      uploadedAttachments.push(args);
      return {
        type: "braintrust_attachment",
        key: "test-key-4",
        filename: args.filename,
        content_type: args.contentType,
      };
    },
    attachments: {},
  });

  expect(uploadedAttachments).toHaveLength(1);
  expect(uploadedAttachments[0].contentType).toBe("image/png");
  expect(uploadedAttachments[0].filename).toBe("file.png");
  expect(uploadedAttachments[0].data).toBeInstanceOf(ArrayBuffer);

  const resultArray = result as unknown as unknown[];
  const imageGenCall = resultArray[0] as AttachmentReference;

  expect(imageGenCall.type).toBe("braintrust_attachment");
  expect(imageGenCall.content_type).toBe("image/png");
  expect(imageGenCall.filename).toBe("file.png");
});
