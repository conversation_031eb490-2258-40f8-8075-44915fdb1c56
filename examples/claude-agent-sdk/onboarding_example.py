#!/usr/bin/env python3

import asyncio
import logging
import os
from typing import Any

from braintrust.wrappers.claude_agent_sdk import setup_claude_agent_sdk
from claude_agent_sdk import ClaudeAgentO<PERSON>s, ClaudeSDKClient, create_sdk_mcp_server, tool

# Enable debug logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s:%(name)s:%(message)s")

# Setup Braintrust - automatically patches claude_agent_sdk for tracing
setup_claude_agent_sdk(
    project="claude-agent-example",
    api_key=os.environ.get("BRAINTRUST_API_KEY"),
)


# Create a calculator tool - automatically traced!
@tool(
    "calculator",
    "Performs basic arithmetic operations",
    {
        "operation": str,
        "a": float,
        "b": float,
    },
)
async def calculator(args: dict[str, Any]) -> dict[str, Any]:
    operation = args["operation"]
    a = float(args["a"])
    b = float(args["b"])

    print(f"[Tool] Calculating: {a} {operation} {b}")

    if operation == "add":
        result = a + b
    elif operation == "subtract":
        result = a - b
    elif operation == "multiply":
        result = a * b
    elif operation == "divide":
        if b == 0:
            return {"content": [{"type": "text", "text": "Error: Division by zero"}], "is_error": True}
        result = a / b
    else:
        result = 0

    return {"content": [{"type": "text", "text": f"The result of {operation}({a}, {b}) is {result}"}]}


async def main():
    print("Starting Claude Agent SDK example with Braintrust tracing...\n")

    try:
        # Create SDK MCP server with the calculator tool
        calculator_server = create_sdk_mcp_server(
            name="calculator",
            version="1.0.0",
            tools=[calculator],
        )

        options = ClaudeAgentOptions(
            model="claude-sonnet-4-5-20250929",
            permission_mode="bypassPermissions",
            mcp_servers={"calculator": calculator_server},
            allowed_tools=["mcp__calculator__calculator"],
        )

        # Use a persistent client session to enable custom MCP tools
        async with ClaudeSDKClient(options=options) as client:
            # First query - multi-step calculation
            print("Query 1: Multi-step calculation\n")
            await client.query("What is 15 multiplied by 7? Then subtract 5 from the result.")
            async for message in client.receive_response():
                print(message)

            print("\n" + "=" * 80 + "\n")

            # Second query - using previous context
            print("Query 2: Follow-up calculation\n")
            await client.query("Now divide that result by 4.")
            async for message in client.receive_response():
                print(message)

        print("\n\n✓ Example completed! Check Braintrust for tracing data.")
    except Exception as error:
        print(f"Error: {error}")
        exit(1)


asyncio.run(main())
