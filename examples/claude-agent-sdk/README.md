# Claude Agent SDK Examples

Examples demonstrating how to use the Claude Agent SDK with Braintrust tracing.

## Setup

### TypeScript

```bash
# Install dependencies in this directory
cd examples/claude-agent-sdk
pnpm install

# Set your API keys
export BRAINTRUST_API_KEY="your-braintrust-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"
```

Note: The TypeScript example uses `workspace:*` to automatically link to the local Braintrust SDK in the monorepo.

### Python

```bash
# Install the local Braintrust SDK in editable mode
pip install -e ../../sdk/py

# Install other dependencies
cd examples/claude-agent-sdk
pip install claude-agent-sdk

# Set your API keys
export BRAINTRUST_API_KEY="your-braintrust-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"
```

## Running the Examples

### TypeScript

```bash
# From this directory
pnpm start

# Or using tsx directly
npx tsx claude-agent.ts
```

### Python

```bash
# Make it executable and run directly
chmod +x claude_agent.py
./claude_agent.py

# Or run with python
python3 claude_agent.py
```

## What the Examples Demonstrate

Both examples show:

1. **Wrapping the Claude Agent SDK** for automatic tracing
2. **Creating tools** with custom handlers
3. **Using the agent** with streaming responses
4. **Tool execution tracing** - each tool call is logged to Braintrust
5. **Error handling** (e.g., division by zero)

The examples ask the agent to perform a multi-step calculation:

- Calculate 15 × 7
- Subtract 5 from the result

This demonstrates the agent using tools multiple times and Braintrust capturing the entire trace.

## Viewing Traces

After running the examples, visit [https://www.braintrust.dev](https://www.braintrust.dev) and navigate to your project to see:

- Agent queries with inputs and outputs
- Individual tool executions
- Performance metrics (time to first token, etc.)
- The full conversation flow
