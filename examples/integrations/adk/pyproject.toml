[project]
name = "googleadk"
version = "0.1.1"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "braintrust>=0.2.6",
    "braintrust-adk",
    "braintrust-local",
    "deprecated>=1.2.18",
    "google-adk>=0.1.0",
    "langfuse>=3.3.4",
    "openinference-instrumentation-google-adk>=0.1.4",
]

[tool.uv.sources]
braintrust-local = { path = "../../../local/py" }
braintrust-adk = { path = "../../../sdk/integrations/adk-py", editable = true }
