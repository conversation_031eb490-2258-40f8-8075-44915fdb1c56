-- Check if retention policies exist in project_automations table
SELECT 
    pa.id,
    pa.project_id,
    p.name as project_name,
    pa.event_type,
    pa.object_type,
    pa.config,
    pa.created,
    pa.updated
FROM project_automations pa
INNER JOIN projects p ON pa.project_id = p.id
WHERE pa.event_type = 'retention'
ORDER BY pa.created DESC;

-- Check retention policy details and cutoff dates
SELECT 
    pa.project_id,
    p.name as project_name,
    pa.object_type,
    pa.config->>'retention_days' as retention_days,
    NOW() - ((pa.config->>'retention_days')::int * INTERVAL '1 day') as cutoff_date,
    pa.created as policy_created
FROM project_automations pa
INNER JOIN projects p ON pa.project_id = p.id
WHERE pa.event_type = 'retention';

-- Check for experiments that should be eligible for deletion
WITH retention_policies AS (
    SELECT
        pa.project_id,
        pa.object_type,
        NOW() - ((pa.config->>'retention_days')::int * INTERVAL '1 day') as cutoff_date
    FROM project_automations pa
    INNER JOIN projects p ON pa.project_id = p.id
    WHERE pa.event_type = 'retention'
)
SELECT 
    COUNT(*) as eligible_experiments,
    MIN(e.created) as oldest_eligible,
    MAX(e.created) as newest_eligible
FROM experiments e
INNER JOIN retention_policies rp ON 
    rp.project_id = e.project_id AND rp.object_type = 'experiment'
WHERE e.deleted_at IS NULL AND e.created < rp.cutoff_date;
