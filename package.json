{"scripts": {"build": "turbo run build", "watch": "turbo run watch", "dev": "turbo run dev", "start": "turbo run start", "start-services": "make services", "stop-services": "make services-down", "start-services-without-web": "BT_SERVICES_FILTER='--exclude webapp' make services", "install-services": "make develop", "run-migrations": "python api-schema/lambda_function.py --execute --run-all-in-foreground", "tail-web-logs": "tail -f $(./services/bt_services.py logfile webapp)", "dev:web": "pnpm run start-services-without-web && cd app && pnpm run dev", "test": "npx vitest run --dir tests", "test-proxy": "cd api-ts && pnpm run test-proxy"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@anthropic-ai/sdk": "^0.52.0", "@braintrust/local": "workspace:*", "@braintrust/openapi-deployment": "workspace:*", "@braintrust/proxy": "workspace:*", "@braintrust/stainless-deployment": "workspace:*", "@braintrust/typespecs": "workspace:*", "@braintrust/vercel-ai-sdk": "workspace:^", "@lancedb/lancedb": "^0.15.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.200.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/exporter-trace-otlp-proto": "^0.200.0", "@opentelemetry/instrumentation": "^0.200.0", "@opentelemetry/otlp-transformer": "^0.200.0", "@opentelemetry/sdk-logs": "^0.200.0", "@opentelemetry/sdk-node": "^0.200.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@vercel/otel": "^1.10.0", "ai": "^4.3.16", "autoevals": "workspace:*", "axios": "^1.11.0", "braintrust": "workspace:*", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "long": "^5.3.2", "openai": "4.104.0", "protobufjs": "^7.5.2", "sqlite3": "^5.1.7", "uuid": "^9.0.1"}, "devDependencies": {"@anthropic-ai/claude-agent-sdk": "^0.1.0", "@braintrust/langchain-js": "workspace:^", "@faker-js/faker": "^9.9.0", "@langchain/core": "^0.3.42", "@langchain/langgraph": "^0.2.25", "@langchain/openai": "^0.3.17", "@mastra/core": "^0.16.3", "@noble/ed25519": "^2.2.3", "@octokit/rest": "^21.1.1", "@openai/realtime-api-beta": "github:openai/openai-realtime-api-beta#cd8a9251dcfb0cba0d7b0501e9ff36c915f5090f", "@types/argparse": "^2.0.17", "@types/glob": "^8.1.0", "@types/jsonwebtoken": "^9.0.9", "@types/lodash.isequal": "^4.5.8", "@types/node": "^22.15.21", "@types/pg": "^8.15.2", "@types/uuid": "^9.0.8", "argparse": "^2.0.1", "depcheck": "^1.4.7", "fumadocs-openapi": "^9.3.8", "glob": "^11.0.2", "jsonwebtoken": "9.0.2", "license-checker": "^25.0.1", "octokit": "^3.2.2", "pg": "^8.16.0", "pyright": "1.1.388", "stacktracify": "^1.0.4", "tailwindcss": "^4.1.13", "tsup": "^8.5.0", "tsx": "^3.14.0", "turbo": "^2.5.3", "typescript": "5.5.4", "vite": "^5.4.19", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.9", "yaml": "^2.8.0", "zod": "3.25.34"}, "resolutions": {"openai": "4.104.0", "typescript": "5.5.4", "zod": "3.25.34", "@asteasolutions/zod-to-openapi>zod": "3.25.34", "@typescript-eslint/parser": "8.11.0"}, "packageManager": "pnpm@8.15.5", "type": "module", "pnpm": {"overrides": {"@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "react": "19.0.0", "react-dom": "19.0.0", "zod": "3.25.34", "@asteasolutions/zod-to-openapi>zod": "3.25.34", "tanu>typescript": "4.9.5"}}}