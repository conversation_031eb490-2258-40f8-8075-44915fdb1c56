[project]
name = "braintrust-adk"
version = "0.2.1"
description = "Braintrust Google ADK integration"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "braintrust>=0.2.4",
    "google-adk>=1.14.1",
    "wrapt>=1.17.3",
]
license = { text = "Apache-2.0" }
authors = [{ name = "Braintrust", email = "<EMAIL>" }]
keywords = ["braintrust", "google-adk", "adk", "agents", "ai", "llm", "tracing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

[project.urls]
Homepage = "https://www.braintrust.dev"
Repository = "https://github.com/braintrustdata/braintrust-sdk"


[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]

[tool.uv.workspace]
members = [
    "examples",
    ".",
]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "pytest-asyncio>=1.1.0",
    "pytest-vcr>=1.0.2",
    "pyyaml>=6.0",
    "ruff>=0.12.9",
]

[tool.isort]
profile = "black"
line_length = 120

[tool.ruff]
line-length = 120

[tool.ruff.lint]
select = [
    "I",    # isort
]

[tool.ruff.lint.isort]
known-first-party = ["braintrust", "braintrust_adk"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v"
asyncio_default_fixture_loop_scope = "function"
