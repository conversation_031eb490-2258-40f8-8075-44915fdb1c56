[project]
name = "braintrust-adk-examples"
version = "0.1.0"
description = "Examples demonstrating braintrust-adk usage patterns for automatic Google ADK tracing"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "braintrust-adk",
    "google-adk>=1.14.1",
    "python-multipart>=0.0.20",
]

[tool.uv.sources]
braintrust-adk = { workspace = true }

[tool.ruff]
line-length = 120

[tool.ruff.lint]
select = [
    "I",    # isort
]

[tool.ruff.lint.isort]
known-first-party = ["braintrust_adk", "braintrust"]
