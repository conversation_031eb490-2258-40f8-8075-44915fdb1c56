#!/bin/bash

# Test retention policy in dry-run mode
# Replace YOUR_API_URL and YOUR_AUTH_TOKEN with actual values

API_URL="http://localhost:8000"  # or your actual API URL
AUTH_TOKEN="your-auth-token-here"

echo "=== Testing Retention Policy Resolution ==="

# First, test if we can resolve retention policies for specific objects
echo "1. Testing retention policy resolution for objects..."
curl -X POST "${API_URL}/api/retention" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${AUTH_TOKEN}" \
  -d '{
    "objects": [
      {
        "object_type": "experiment",
        "object_id": "00000000-0000-0000-0000-000000000000"
      }
    ],
    "fail_unknown_objects": false
  }' | jq .

echo -e "\n2. Testing retention policy (dry run)..."
curl -X POST "${API_URL}/api/retention/metadata/cron" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${AUTH_TOKEN}" \
  -d '{
    "dry_run": true,
    "batch_size": 100
  }' | jq .

echo -e "\n3. Testing retention policy for specific org (dry run)..."
curl -X POST "${API_URL}/api/retention/metadata/cron" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${AUTH_TOKEN}" \
  -d '{
    "dry_run": true,
    "batch_size": 100,
    "org_id": "your-org-id-here"
  }' | jq .

echo -e "\n=== Docker Container Status ==="
echo "4. Checking container status..."
docker ps --filter "name=braintrust" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo -e "\n=== Brainstore Logs (last 20 lines) ==="
echo "5. Recent Brainstore logs..."
docker logs --tail 20 braintrust-brainstore 2>/dev/null || echo "Brainstore container not found or not running"

echo -e "\n=== API Logs (last 20 lines) ==="
echo "6. Recent API logs..."
docker logs --tail 20 braintrust-standalone-api 2>/dev/null || echo "API container not found or not running"

echo -e "\n=== Environment Variables Check ==="
echo "7. Checking Brainstore environment..."
docker exec braintrust-brainstore env | grep -E "(BRAINSTORE_|RETENTION)" 2>/dev/null || echo "Cannot access Brainstore environment"

echo -e "\n8. Checking API environment..."
docker exec braintrust-standalone-api env | grep -E "(BRAINSTORE_|RETENTION)" 2>/dev/null || echo "Cannot access API environment"

echo -e "\n\nTo run actual deletion (remove dry_run or set to false):"
echo "curl -X POST \"${API_URL}/api/retention/metadata/cron\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer ${AUTH_TOKEN}\" \\"
echo "  -d '{ \"dry_run\": false, \"batch_size\": 100 }'"
