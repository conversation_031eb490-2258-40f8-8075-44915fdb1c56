import braintrust

# Initialize/fetch the dataset
dataset = braintrust.init_dataset(project="pedro-project1", name="Dataset 38")

# Method 1: If you know the ID of the row you want to delete
row_id_to_delete = "b7ff0bc9-b454-45e2-bf6c-2bc96fdb1668"
dataset.delete(row_id_to_delete)

# Method 2: Find a row based on some criteria and delete it
# for row in dataset:
#     # Example: delete rows where input equals a specific value
#     if row.get('input') == 'some_value_to_delete':
#         dataset.delete(row['id'])
#         break  # Delete only the first match

# # Method 3: Using fetch() method explicitly
# for row in dataset.fetch():
#     if row.get('metadata', {}).get('foo') == 'delete_me':
#         dataset.delete(row['id'])
#         break

# Don't forget to flush to ensure the deletion is sent to the server
dataset.flush()