import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import {
  Bug,
  Edit3,
  <PERSON><PERSON><PERSON><PERSON>,
  Trash,
  Cylind<PERSON><PERSON><PERSON>,
  <PERSON>lip<PERSON>,
  Ellip<PERSON>,
} from "lucide-react";
import { type ReactNode, useMemo, useState } from "react";
import { ObjectPermissionsDialog } from "#/app/app/[org]/p/[project]/permissions/object-permissions-dialog";
import { type AclObjectType } from "@braintrust/typespecs";
import { downloadAsJSON } from "#/utils/download";
import { backfillableObjectTypeSchema } from "@braintrust/local/app-schema";
import { BrainstoreObjectConfiguration } from "#/app/app/[org]/p/[project]/brainstore/[object]/brainstore-object-configuration";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useBrainstoreBackfillState } from "./query-parameters";
import { type GetRowsForExportFn } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(queries)/table-queries";
import { type ParsedQuery } from "@braintrust/btql/parser";
import { cn } from "#/utils/classnames";
import { useDownloadRowsMenu } from "./download";

export const EntityContextMenu = ({
  objectName,
  objectId,
  objectType,
  orgName,
  projectName,
  handleEdit,
  handleDelete,
  handleCopyId,
  handleCopyName,
  extraOptions,
  buttonClassName,
  renderDropdownItemsOnly,
  excludePermissions,
  getRowsForExport,
  getRawData,
  exportName,
  refetchDataQueryFn,
  downloadColumnVisibility,
  isReadOnly,
  isPlayground,
  rowCount,
  downloadDisabled,
}: {
  objectName: string;
  objectId: string;
  objectType: AclObjectType;
  orgName: string;
  projectName: string;
  handleEdit?: VoidFunction;
  handleDelete?: VoidFunction;
  handleCopyId?: VoidFunction;
  handleCopyName?: VoidFunction;
  buttonClassName?: string;
  extraOptions?: {
    label: ReactNode;
    disabled?: boolean;
    onClick: (() => void) | (() => Promise<void>);
  }[];
  renderDropdownItemsOnly?: boolean;
  excludePermissions?: boolean;
  getRowsForExport?: GetRowsForExportFn;
  getRawData?: () => Promise<unknown[] | undefined>;
  exportName?: string;
  refetchDataQueryFn?: (rowIds?: string[]) => ParsedQuery | undefined;
  downloadColumnVisibility?: Record<string, boolean>;
  isReadOnly?: boolean;
  isPlayground?: boolean;
  rowCount?: number;
  downloadDisabled?: boolean;
}) => {
  const [isPermissionsOpen, setIsPermissionsOpen] = useState(false);
  const [isBackfillStatusOpen, setIsBackfillStatusOpen] =
    useBrainstoreBackfillState();

  const backfillableObjectType = useMemo(() => {
    const ordinaryObjectType =
      objectType === "project_log"
        ? "project_logs"
        : objectType === "prompt_session"
          ? "playground_logs"
          : objectType;
    return backfillableObjectTypeSchema.safeParse(ordinaryObjectType);
  }, [objectType]);

  const {
    flags: { brainstore },
  } = useFeatureFlags();

  const { downloadMenu, confirmDownloadDialog } = useDownloadRowsMenu({
    buttonText: "Download rows",
    exportName: exportName ?? objectName ?? objectType,
    refetchDataQueryFn,
    getRowsForExport,
    columnVisibility: downloadColumnVisibility,
    isPlayground,
    submenu: true,
    rowCount,
    disabled: downloadDisabled,
  });

  const label =
    objectType === "prompt_session"
      ? "playground"
      : objectType === "project_log"
        ? "project"
        : objectType;

  const items = (
    <>
      {!isReadOnly && handleEdit && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleEdit();
          }}
        >
          <Edit3 className="size-3" /> Rename {label}
        </DropdownMenuItem>
      )}
      {downloadMenu}
      {!isReadOnly && getRawData && (
        <DropdownMenuItem
          onClick={async () => {
            const data = await getRawData();
            if (!data) return;
            downloadAsJSON(
              (exportName ?? objectName ?? objectType) + ".json",
              data,
            );
          }}
        >
          <>
            <Bug className="size-3" /> Download debug data
          </>
        </DropdownMenuItem>
      )}
      {extraOptions?.map((option, idx) => (
        <DropdownMenuItem
          key={idx}
          disabled={option.disabled}
          onClick={(e) => {
            e.stopPropagation();
            option.onClick();
          }}
        >
          {option.label}
        </DropdownMenuItem>
      ))}
      {handleCopyId && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleCopyId();
          }}
        >
          <Clipboard className="size-3" />
          Copy {label} ID
        </DropdownMenuItem>
      )}
      {handleCopyName && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleCopyName();
          }}
        >
          <Clipboard className="size-3" />
          Copy {label} name
        </DropdownMenuItem>
      )}
      {!isReadOnly && !excludePermissions && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            setIsPermissionsOpen(true);
          }}
        >
          <ShieldCheck className="size-3" />
          Permissions
        </DropdownMenuItem>
      )}
      {!isReadOnly && brainstore && backfillableObjectType.success && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            setIsBackfillStatusOpen(true);
          }}
        >
          <CylinderIcon className="size-3" /> Backfill status
        </DropdownMenuItem>
      )}
      {!isReadOnly && handleDelete && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleDelete();
          }}
        >
          <Trash className="size-3" /> Delete {label}
        </DropdownMenuItem>
      )}
    </>
  );
  return (
    <>
      {renderDropdownItemsOnly ? (
        items
      ) : (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              size="xs"
              variant="ghost"
              className={cn("px-1", buttonClassName)}
            >
              <Ellipsis className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent hideWhenDetached align="start">
            {items}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {isBackfillStatusOpen && backfillableObjectType.success && (
        <BrainstoreObjectConfiguration
          onOpenChange={setIsBackfillStatusOpen}
          objectId={`${backfillableObjectType.data}:${objectId}`}
        />
      )}
      {!excludePermissions && (
        <ObjectPermissionsDialog
          open={isPermissionsOpen}
          onOpenChange={setIsPermissionsOpen}
          orgName={orgName}
          projectName={projectName}
          objectId={objectId}
          objectName={objectName}
          objectType={objectType}
        />
      )}
      {confirmDownloadDialog}
    </>
  );
};
