import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { type GetRowsForExportFn } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(queries)/table-queries";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "./dropdown-menu";
import { Button, type ButtonProps } from "./button";
import { type ParsedQuery } from "@braintrust/btql/parser";
import {
  DOWNLOAD_ROW_LIMIT,
  downloadAsCSV,
  downloadAsJSON,
} from "#/utils/download";
import { Download, FileJson, FileSpreadsheet } from "lucide-react";
import { toast } from "sonner";
import { ConfirmationDialog } from "./dialogs/confirmation";
import { cn } from "#/utils/classnames";

type DownloadMenuItemsProps = {
  exportName: string;
  refetchDataQueryFn?: (rowIds?: string[]) => ParsedQuery | undefined;
  getRowsForExport: GetRowsForExportFn | undefined;
  selectedRows?: Record<string, boolean>;
  columnVisibility?: Record<string, boolean>;
  isPlayground?: boolean;
  submenu?: boolean;
  rowCount?: number;
  disabled?: boolean;
};

type DownloadParams = {
  visibility?: Record<string, boolean>;
  downloadType: "csv" | "json";
};

export const useDownloadRowsMenu = ({
  buttonVariant,
  buttonText = "Download",
  exportName,
  refetchDataQueryFn,
  getRowsForExport,
  selectedRows,
  columnVisibility,
  isPlayground,
  submenu,
  rowCount,
  disabled,
}: DownloadMenuItemsProps & {
  buttonVariant?: ButtonProps["variant"];
  buttonText?: string;
}) => {
  const [confirmDownload, setConfirmDownload] = useState<
    DownloadParams | undefined
  >();
  const selectedRowIds = useMemo(
    () => (selectedRows ? Object.keys(selectedRows ?? {}) : undefined),
    [selectedRows],
  );
  const downloadRows = useCallback(
    (downloadParams: DownloadParams, skipRowCheck?: boolean) => {
      const rows = selectedRowIds ? selectedRowIds.length : rowCount;
      if (rows && rows > DOWNLOAD_ROW_LIMIT && !skipRowCheck) {
        setConfirmDownload(downloadParams);
        return;
      }
      const { visibility, downloadType } = downloadParams;
      toast.promise<{ rows?: number }>(
        async () => {
          try {
            const refetchDataQuery = refetchDataQueryFn?.(
              selectedRowIds && selectedRowIds.length > 0
                ? selectedRowIds
                : undefined,
            );
            const data = await getRowsForExport?.({
              withComparisons: isPlayground,
              refetchDataQuery,
              columnVisibility: visibility,
              rowIds:
                selectedRowIds && selectedRowIds.length > 0
                  ? selectedRowIds
                  : undefined,
            });

            if (!data) {
              throw new Error(`No rows found`);
            }
            if (downloadType === "csv") {
              downloadAsCSV(exportName, data);
            } else {
              downloadAsJSON(exportName, data);
            }
            return { rows: data.length };
          } catch (error) {
            console.error(error);
            throw error;
          }
        },
        {
          loading: `Downloading ${exportName} rows`,
          success: (data) =>
            data.rows ? `Downloaded ${data.rows} rows` : "Download complete",
          error: (data) =>
            `Failed to download rows for ${exportName}: ${data?.message ?? ""}`,
          closeButton: false,
        },
      );
    },
    [
      selectedRowIds,
      refetchDataQueryFn,
      getRowsForExport,
      isPlayground,
      exportName,
      rowCount,
    ],
  );

  const menuItems = useMemo(() => {
    return [
      {
        downloadType: "csv" as const,
      },
      {
        downloadType: "json" as const,
      },
    ].map((item, i) => {
      const IconComponent =
        item.downloadType === "csv" ? FileSpreadsheet : FileJson;
      return (
        <DropdownMenuItem
          key={i}
          onClick={() => {
            downloadRows({
              visibility: columnVisibility,
              downloadType: item.downloadType,
            });
          }}
        >
          <>
            <IconComponent className="size-3" />
            Download as {item.downloadType.toUpperCase()}
          </>
        </DropdownMenuItem>
      );
    });
  }, [columnVisibility, downloadRows]);

  const downloadMenu = useMemo(() => {
    if (submenu) {
      return (
        <DropdownMenuSub>
          <DropdownMenuSubTrigger
            disabled={disabled}
            className={cn({
              "opacity-50": disabled,
            })}
          >
            <>
              <Download className="size-3" />
              {buttonText}
            </>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>{menuItems}</DropdownMenuSubContent>
        </DropdownMenuSub>
      );
    }

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={disabled}>
          <Button
            size="xs"
            Icon={Download}
            variant={buttonVariant}
            title={buttonText}
          />
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">{menuItems}</DropdownMenuContent>
      </DropdownMenu>
    );
  }, [buttonText, buttonVariant, menuItems, submenu, disabled]);

  const confirmDownloadDialog = useMemo(
    () => (
      <ConfirmationDialog
        open={!!confirmDownload}
        onOpenChange={(v) => !v && setConfirmDownload(undefined)}
        title="Download row limit"
        description="Your table view has more than 1000 rows. To download more than 1000 rows, please use the API."
        confirmText="Download 1000 rows"
        onConfirm={() => {
          if (!confirmDownload) {
            return;
          }
          downloadRows(confirmDownload, true);
        }}
      />
    ),
    [confirmDownload, downloadRows],
  );

  return getRowsForExport
    ? {
        downloadMenu,
        confirmDownloadDialog,
      }
    : {};
};
