"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { Input } from "#/ui/input";
import { Spinner } from "#/ui/icons/spinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "#/ui/select";
import { useAuth } from "@clerk/nextjs";
import { useState } from "react";
import { toast } from "sonner";
import { Trash } from "lucide-react";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useQueryFunc } from "#/utils/react-query";
import {
  type adminFetchDomainMappings,
  type adminCreateDomainMapping,
  type adminDeleteDomainMapping,
  type adminFetchOrgGroups,
} from "../../actions";

interface DomainMappingsProps {
  orgId: string;
}

export function DomainMappings({ orgId }: DomainMappingsProps) {
  const { getToken } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [newDomain, setNewDomain] = useState("");
  const [selectedGroupId, setSelectedGroupId] =
    useState<string>("__no_group__");
  const [newSamlGroup, setNewSamlGroup] = useState("");

  const { data: domainMappings, invalidate: refreshDomainMappings } =
    useQueryFunc<typeof adminFetchDomainMappings>({
      fName: "adminFetchDomainMappings",
      args: { orgId },
    });

  const { data: groups } = useQueryFunc<typeof adminFetchOrgGroups>({
    fName: "adminFetchOrgGroups",
    args: { orgId },
  });

  const handleCreateDomainMapping = async () => {
    if (!newDomain.trim()) {
      toast("Please enter a domain");
      return;
    }

    setIsCreating(true);
    try {
      await invokeServerAction<typeof adminCreateDomainMapping>({
        fName: "adminCreateDomainMapping",
        args: {
          orgId,
          domain: newDomain.trim(),
          groupId: selectedGroupId === "__no_group__" ? null : selectedGroupId,
          samlGroup: newSamlGroup.trim() || null,
        },
        getToken,
      });

      setNewDomain("");
      setSelectedGroupId("__no_group__");
      setNewSamlGroup("");
      await refreshDomainMappings();
      toast("Domain mapping created successfully");
    } catch (error) {
      console.error("Failed to create domain mapping:", error);
      toast("Failed to create domain mapping");
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteDomainMapping = async (id: string) => {
    try {
      await invokeServerAction<typeof adminDeleteDomainMapping>({
        fName: "adminDeleteDomainMapping",
        args: { id },
        getToken,
      });

      await refreshDomainMappings();
      toast("Domain mapping deleted successfully");
    } catch (error) {
      console.error("Failed to delete domain mapping:", error);
      toast("Failed to delete domain mapping");
    }
  };

  const getGroupName = (groupId: string | null): string => {
    if (!groupId) return "No group (all users)";
    const group = groups?.find((g) => g.id === groupId);
    return group?.name || "Unknown group";
  };

  return (
    <div className="space-y-4">
      <div className="text-xs font-medium text-primary-500">
        Domain Mappings
      </div>

      {/* Current mappings */}
      <div className="space-y-2">
        {!domainMappings ? (
          <div className="flex items-center gap-2">
            <Spinner className="size-3" />
            <span className="text-sm text-primary-500">Loading...</span>
          </div>
        ) : domainMappings.length === 0 ? (
          <div className="text-sm text-primary-500">
            No domain mappings configured
          </div>
        ) : (
          domainMappings.map((mapping) => (
            <div
              key={mapping.id}
              className="flex items-center justify-between rounded-md bg-primary-50 px-3 py-2"
            >
              <div className="flex-1">
                <div className="text-sm font-medium">{mapping.domain}</div>
                <div className="text-xs text-primary-500">
                  → {getGroupName(mapping.group_id)}
                  {mapping.saml_group && (
                    <span className="ml-2">
                      (SAML group: {mapping.saml_group})
                    </span>
                  )}
                </div>
              </div>
              <Button
                size="xs"
                variant="ghost"
                onClick={() => handleDeleteDomainMapping(mapping.id)}
                IconLeft={Trash}
                className="text-red-600 hover:text-red-700"
              >
                Delete
              </Button>
            </div>
          ))
        )}
      </div>

      {/* Add new mapping form */}
      <div className="border-t border-primary-200 pt-4">
        <div className="mb-2 text-xs font-medium text-primary-500">
          Add Domain Mapping
        </div>
        <div className="space-y-3">
          <div>
            <Input
              placeholder="Enter domain (e.g., example.com)"
              value={newDomain}
              onChange={(e) => setNewDomain(e.target.value)}
              className="w-full"
            />
          </div>
          <div>
            <Select value={selectedGroupId} onValueChange={setSelectedGroupId}>
              <SelectTrigger>
                <SelectValue placeholder="Select group (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__no_group__">
                  No group (all users)
                </SelectItem>
                {groups?.map((group) => (
                  <SelectItem key={group.id} value={group.id}>
                    {group.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Input
              placeholder="Enter SAML group (optional)"
              value={newSamlGroup}
              onChange={(e) => setNewSamlGroup(e.target.value)}
              className="w-full"
            />
          </div>
          <div>
            <Button
              size="xs"
              onClick={handleCreateDomainMapping}
              disabled={isCreating || !newDomain.trim()}
            >
              {isCreating ? (
                <>
                  <Spinner className="mr-2 size-3" />
                  Creating...
                </>
              ) : (
                "Add Domain Mapping"
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
