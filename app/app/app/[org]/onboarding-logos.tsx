import { type SVGProps } from "react";

export const TypescriptLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fill="currentColor"
        d="M1.125 0C.502 0 0 .502 0 1.125v21.75C0 23.498.502 24 1.125 24h21.75c.623 0 1.125-.502 1.125-1.125V1.125C24 .502 23.498 0 22.875 0zm17.363 9.75c.612 0 1.154.037 1.627.111a6.38 6.38 0 0 1 1.306.34v2.458a3.95 3.95 0 0 0-.643-.361 5.093 5.093 0 0 0-.717-.26 5.453 5.453 0 0 0-1.426-.2c-.3 0-.573.028-.819.086a2.1 2.1 0 0 0-.623.242c-.17.104-.3.229-.393.374a.888.888 0 0 0-.14.49c0 .196.053.373.156.529.104.156.252.304.443.444s.423.276.696.41c.273.135.582.274.926.416.47.197.892.407 1.266.628.374.222.695.473.963.753.268.279.472.598.614.957.142.359.214.776.214 1.253 0 .657-.125 1.21-.373 1.656a3.033 3.033 0 0 1-1.012 1.085 4.38 4.38 0 0 1-1.487.596c-.566.12-1.163.18-1.79.18a9.916 9.916 0 0 1-1.84-.164 5.544 5.544 0 0 1-1.512-.493v-2.63a5.033 5.033 0 0 0 3.237 1.2c.333 0 .624-.03.872-.09.249-.06.456-.144.623-.25.166-.108.29-.234.373-.38a1.023 1.023 0 0 0-.074-1.089 2.12 2.12 0 0 0-.537-.5 5.597 5.597 0 0 0-.807-.444 27.72 27.72 0 0 0-1.007-.436c-.918-.383-1.602-.852-2.053-1.405-.45-.553-.676-1.222-.676-2.005 0-.614.123-1.141.369-1.582.246-.441.58-.804 1.004-1.089a4.494 4.494 0 0 1 1.47-.629 7.536 7.536 0 0 1 1.77-.201zm-15.113.188h9.563v2.166H9.506v9.646H6.789v-9.646H3.375z"
      />
    </svg>
  );
};
export const PythonLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fill="currentColor"
        d="M14.25.18l.9.2.73.26.59.3.45.32.34.34.25.34.16.33.1.3.04.26.02.2-.01.13V8.5l-.05.63-.13.55-.21.46-.26.38-.3.31-.33.25-.35.19-.35.14-.33.1-.3.07-.26.04-.21.02H8.77l-.69.05-.59.14-.5.22-.41.27-.33.32-.27.35-.2.36-.15.37-.1.35-.07.32-.04.27-.02.21v3.06H3.17l-.21-.03-.28-.07-.32-.12-.35-.18-.36-.26-.36-.36-.35-.46-.32-.59-.28-.73-.21-.88-.14-1.05-.05-1.23.06-1.22.16-1.04.24-.87.32-.71.36-.57.4-.44.42-.33.42-.24.4-.16.36-.1.32-.05.24-.01h.16l.06.01h8.16v-.83H6.18l-.01-2.75-.02-.37.05-.34.11-.31.17-.28.25-.26.31-.23.38-.2.44-.18.51-.15.58-.12.64-.1.71-.06.77-.04.84-.02 1.27.05zm-6.3 1.98l-.23.33-.08.41.08.41.23.34.33.22.41.09.41-.09.33-.22.23-.34.08-.41-.08-.41-.23-.33-.33-.22-.41-.09-.41.09zm13.09 3.95l.28.06.32.12.35.18.36.27.36.35.35.47.32.59.28.73.21.88.14 1.04.05 1.23-.06 1.23-.16 1.04-.24.86-.32.71-.36.57-.4.45-.42.33-.42.24-.4.16-.36.09-.32.05-.24.02-.16-.01h-8.22v.82h5.84l.01 2.76.02.36-.05.34-.11.31-.17.29-.25.25-.31.24-.38.2-.44.17-.51.15-.58.13-.64.09-.71.07-.77.04-.84.01-1.27-.04-1.07-.14-.9-.2-.73-.25-.59-.3-.45-.33-.34-.34-.25-.34-.16-.33-.1-.3-.04-.25-.02-.2.01-.13v-5.34l.05-.64.13-.54.21-.46.26-.38.3-.32.33-.24.35-.2.35-.14.33-.1.3-.06.26-.04.21-.02.13-.01h5.84l.69-.05.59-.14.5-.21.41-.28.33-.32.27-.35.2-.36.15-.36.1-.35.07-.32.04-.28.02-.21V6.07h2.09l.14.01zm-6.47 14.25l-.23.33-.08.41.08.41.23.33.33.23.41.08.41-.08.33-.23.23-.33.08-.41-.08-.41-.23-.33-.33-.23-.41-.08-.41.08z"
      />
    </svg>
  );
};

export const VercelLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path d="M24 5.78906L45 42.1625H3L24 5.78906Z" fill="currentColor" />
    </svg>
  );
};

export const TraceloopLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_9_21)">
        {/* <path d="M47 9H1V39.8908H47V9Z" fill="#FF3B30" /> */}
        <path
          d="M4.48898 9.02734C4.57523 9.02686 4.66148 9.02638 4.75034 9.02589C5.03908 9.02455 5.3278 9.02472 5.61654 9.0249C5.82368 9.02426 6.03082 9.02354 6.23795 9.02274C6.8005 9.02087 7.36305 9.02037 7.9256 9.02024C8.27714 9.02009 8.62867 9.01964 8.98021 9.01907C10.2068 9.01712 11.4335 9.01625 12.6601 9.01642C13.8031 9.01654 14.9461 9.01431 16.0891 9.01097C17.0707 9.0082 18.0524 9.00706 19.034 9.0072C19.6202 9.00725 20.2063 9.00665 20.7924 9.0044C21.3439 9.00235 21.8953 9.00234 22.4467 9.00388C22.6488 9.0041 22.8509 9.0036 23.053 9.00232C23.3295 9.00068 23.6059 9.00167 23.8824 9.0033C24.0016 9.00167 24.0016 9.00167 24.1232 9C24.766 9.00834 25.3143 9.19718 25.8034 9.62121C26.2017 10.0538 26.3767 10.5022 26.3947 11.0849C26.3592 11.7062 26.1639 12.1882 25.7465 12.6558C25.1141 13.1409 24.5121 13.201 23.7384 13.2001C23.654 13.2004 23.5696 13.2007 23.4826 13.201C23.1995 13.202 22.9163 13.2021 22.6332 13.2023C22.4303 13.2028 22.2273 13.2033 22.0244 13.2039C21.4729 13.2053 20.9214 13.2059 20.3699 13.2064C20.0253 13.2067 19.6807 13.2072 19.3361 13.2076C18.2577 13.2091 17.1794 13.2101 16.1011 13.2105C14.8565 13.211 13.612 13.2128 12.3675 13.2159C11.4054 13.2182 10.4432 13.2193 9.4811 13.2194C8.90654 13.2195 8.33198 13.2202 7.75742 13.222C7.21679 13.2238 6.67618 13.224 6.13555 13.2231C5.93741 13.223 5.73926 13.2235 5.54112 13.2245C5.27002 13.2259 4.99899 13.2252 4.72789 13.2241C4.65001 13.225 4.57213 13.2258 4.49189 13.2267C3.79979 13.2202 3.2193 13.0627 2.68559 12.6161C2.16125 11.9926 2.07352 11.5723 2.0937 10.7676C2.16013 10.1745 2.38053 9.85847 2.81916 9.44611C3.37023 9.0865 3.83829 9.02468 4.48898 9.02734Z"
          fill={props.color || "#FF3D5D"}
        />
        <path
          d="M32.2201 9.02665C32.3928 9.0253 32.3928 9.0253 32.569 9.02392C32.6947 9.02402 32.8204 9.02411 32.9499 9.02421C33.1492 9.02315 33.1492 9.02315 33.3525 9.02206C33.7166 9.0204 34.0808 9.0197 34.4449 9.01957C34.6727 9.01942 34.9005 9.01897 35.1282 9.01841C35.9237 9.01646 36.7191 9.01559 37.5146 9.01576C38.2547 9.01589 38.9947 9.01366 39.7348 9.01032C40.3712 9.00756 41.0076 9.00643 41.644 9.00657C42.0237 9.00662 42.4033 9.00603 42.7829 9.00378C43.2069 9.00171 43.6308 9.00232 44.0548 9.0036C44.1796 9.00241 44.3044 9.00122 44.4329 9C45.2491 9.00621 45.8972 9.09922 46.5038 9.66888C46.544 9.72971 46.5842 9.79053 46.6256 9.8532C46.6889 9.94485 46.6889 9.94485 46.7535 10.0383C47.0348 10.5333 47.0261 11.0413 46.9655 11.5884C46.7674 12.2109 46.4183 12.6039 45.8557 12.9607C45.1739 13.2031 44.5286 13.2031 43.8061 13.2025C43.6801 13.2032 43.554 13.2039 43.4241 13.2047C43.0085 13.2067 42.5929 13.2071 42.1773 13.2073C41.8875 13.208 41.5977 13.2087 41.3078 13.2094C40.7006 13.2108 40.0934 13.2111 39.4862 13.211C38.7866 13.2108 38.0871 13.2131 37.3875 13.2164C36.7119 13.2195 36.0363 13.2202 35.3608 13.2201C35.0745 13.2204 34.7883 13.2213 34.5021 13.2229C34.101 13.225 33.7001 13.2244 33.299 13.2231C33.1818 13.2243 33.0645 13.2255 32.9438 13.2267C32.135 13.2202 31.3378 13.108 30.69 12.608C30.2721 12.1738 30.1009 11.7008 30.0931 11.1179C30.1391 10.3878 30.3479 9.94816 30.9289 9.45074C31.3749 9.15377 31.6708 9.0286 32.2201 9.02665Z"
          fill={props.color || "#FFE600"}
        />
        <path
          d="M5.03926 16.9275C5.16432 16.9269 5.28938 16.9263 5.41823 16.9256C5.82925 16.9241 6.24018 16.9261 6.6512 16.9282C6.93808 16.9282 7.22495 16.928 7.51183 16.9277C8.11206 16.9274 8.71227 16.929 9.3125 16.9317C10.0046 16.9349 10.6967 16.9351 11.3889 16.9337C12.0572 16.9324 12.7255 16.9331 13.3939 16.9348C13.6772 16.9354 13.9605 16.9354 14.2438 16.9349C14.64 16.9346 15.0361 16.9366 15.4323 16.9391C15.5487 16.9386 15.6652 16.9381 15.7852 16.9375C16.5444 16.9457 17.2112 17.0104 17.8127 17.5736C17.8718 17.6545 17.9309 17.7353 17.9918 17.8186C18.0529 17.8995 18.114 17.9803 18.1769 18.0636C18.534 18.6288 18.4876 19.2972 18.4302 19.9567C18.2607 20.6257 17.9456 21.0162 17.4249 21.3928C17.0703 21.6038 16.7772 21.6351 16.3824 21.6372C16.2715 21.638 16.1606 21.6389 16.0464 21.6398C15.9248 21.6401 15.8032 21.6404 15.6779 21.6407C15.4857 21.6419 15.4857 21.6419 15.2897 21.6431C14.8647 21.6454 14.4396 21.6468 14.0146 21.648C13.8681 21.6485 13.7216 21.649 13.575 21.6495C12.8864 21.6517 12.1977 21.6534 11.509 21.6544C10.7153 21.6556 9.92154 21.6587 9.12778 21.6635C8.51355 21.6671 7.89933 21.6688 7.28509 21.6692C6.91858 21.6695 6.55211 21.6706 6.18561 21.6736C5.77617 21.6765 5.36682 21.6764 4.95738 21.6755C4.83687 21.677 4.71637 21.6786 4.59222 21.6801C3.8228 21.6745 3.18912 21.5714 2.61506 20.9684C2.10657 20.3294 2.08396 19.7517 2.09226 18.9318C2.15818 18.3179 2.35765 17.8685 2.78274 17.4623C3.52364 16.98 4.19289 16.9192 5.03926 16.9275Z"
          fill={props.color || "#FFA42C"}
        />
        <path
          d="M16.3365 33.342C16.4473 33.3412 16.5582 33.3404 16.6723 33.3397C16.8546 33.3392 16.8546 33.3392 17.0406 33.3388C17.1686 33.3381 17.2967 33.3374 17.4286 33.3367C17.8533 33.3346 18.2781 33.3334 18.7029 33.3323C18.8493 33.3319 18.9957 33.3314 19.1422 33.331C19.8304 33.329 20.5187 33.3275 21.2069 33.3266C22.0002 33.3256 22.7934 33.3228 23.5867 33.3185C24.2005 33.3153 24.8144 33.3137 25.4282 33.3134C25.7945 33.3131 26.1608 33.3122 26.527 33.3095C26.9362 33.3069 27.3453 33.307 27.7545 33.3078C27.8749 33.3064 27.9954 33.305 28.1195 33.3036C28.9365 33.309 29.5335 33.4228 30.1536 33.9865C30.6132 34.6485 30.6877 35.2152 30.5749 36.0056C30.3924 36.5749 30.0015 36.9569 29.4964 37.2766C28.8782 37.5449 28.2744 37.5191 27.6115 37.5176C27.4876 37.5181 27.3637 37.5187 27.2361 37.5192C26.8271 37.5207 26.4181 37.5206 26.0091 37.5203C25.7243 37.5208 25.4396 37.5212 25.1548 37.5217C24.5581 37.5226 23.9614 37.5225 23.3647 37.5219C22.6765 37.5213 21.9883 37.5226 21.3001 37.5248C20.6363 37.5269 19.9725 37.5272 19.3086 37.5269C19.027 37.5269 18.7454 37.5275 18.4637 37.5286C18.0696 37.5299 17.6755 37.5291 17.2813 37.5279C17.1654 37.5287 17.0495 37.5295 16.9301 37.5304C16.1249 37.5248 15.4856 37.4468 14.8494 36.9192C14.3245 36.3004 14.2251 35.8769 14.2453 35.0771C14.3327 34.407 14.6347 34.0362 15.1662 33.629C15.5788 33.4087 15.8742 33.3441 16.3365 33.342Z"
          fill={props.color || "#FF3D5D"}
        />
        <path
          d="M32.2017 25.4304C32.3757 25.429 32.3757 25.429 32.5532 25.4276C32.7441 25.4269 32.7441 25.4269 32.9389 25.4262C33.073 25.4253 33.207 25.4244 33.3452 25.4235C33.79 25.4208 34.2347 25.419 34.6795 25.4175C34.8328 25.4169 34.9862 25.4163 35.1395 25.4157C35.7776 25.4134 36.4157 25.4114 37.0537 25.4101C37.9669 25.4084 38.8801 25.4048 39.7933 25.3986C40.436 25.3944 41.0787 25.3923 41.7215 25.3917C42.105 25.3913 42.4885 25.39 42.8721 25.3865C43.3005 25.3826 43.7288 25.3831 44.1573 25.3839C44.2834 25.3821 44.4096 25.3803 44.5395 25.3785C45.301 25.3843 45.8361 25.4868 46.4165 25.9886C46.9487 26.6536 47.0623 27.2214 46.972 28.0462C46.756 28.7179 46.3881 29.1056 45.7686 29.4633C45.48 29.582 45.2645 29.5899 44.9516 29.5906C44.7768 29.5918 44.7768 29.5918 44.5985 29.593C44.4713 29.5927 44.3441 29.5924 44.213 29.5921C44.0786 29.5927 43.9441 29.5933 43.8056 29.5938C43.4372 29.5951 43.0688 29.5953 42.7004 29.5951C42.3925 29.5949 42.0847 29.5955 41.7769 29.596C41.0504 29.5972 40.3239 29.5972 39.5975 29.5966C38.8488 29.5959 38.1001 29.5973 37.3513 29.5996C36.7079 29.6015 36.0646 29.6021 35.4212 29.6018C35.0371 29.6016 34.6531 29.6019 34.2691 29.6034C33.8407 29.605 33.4122 29.604 32.9838 29.6027C32.7936 29.604 32.7936 29.604 32.5997 29.6052C31.869 29.6005 31.3401 29.5527 30.7706 29.0628C30.2793 28.4904 30.0533 28.0498 30.0988 27.2881C30.1872 26.628 30.4731 26.2256 30.9807 25.7923C31.4056 25.4839 31.6867 25.4335 32.2017 25.4304Z"
          fill={props.color || "#FFE600"}
        />
        <path
          d="M36.92 33.3165C37.0095 33.3159 37.099 33.3154 37.1913 33.3148C37.4859 33.3134 37.7804 33.3135 38.0749 33.3137C38.2806 33.3133 38.4863 33.3128 38.6919 33.3123C39.1225 33.3115 39.553 33.3116 39.9836 33.3121C40.5342 33.3127 41.0848 33.3109 41.6354 33.3085C42.06 33.3069 42.4846 33.3068 42.9092 33.3072C43.1122 33.3071 43.3151 33.3065 43.5181 33.3055C43.8023 33.3042 44.0865 33.3049 44.3708 33.3061C44.4539 33.3053 44.537 33.3045 44.6226 33.3036C45.2811 33.3099 45.8119 33.4385 46.3179 33.8701C46.3798 33.9424 46.4418 34.0147 46.5056 34.0892C46.5692 34.1615 46.6329 34.2338 46.6984 34.3083C47.053 34.7851 47.0238 35.3338 46.9622 35.895C46.7641 36.5229 46.4137 36.9115 45.8552 37.2759C45.3333 37.4836 44.8544 37.5193 44.2965 37.5175C44.1656 37.5183 44.1656 37.5183 44.0322 37.5192C43.7454 37.5207 43.4586 37.5206 43.1717 37.5203C42.9713 37.5207 42.7708 37.5212 42.5704 37.5217C42.1507 37.5225 41.7311 37.5225 41.3114 37.5219C40.7752 37.5213 40.239 37.5231 39.7028 37.5255C39.2889 37.5271 38.8751 37.5272 38.4612 37.5269C38.2636 37.5269 38.066 37.5275 37.8683 37.5285C37.5913 37.5299 37.3143 37.5291 37.0372 37.5279C36.9162 37.5291 36.9162 37.5291 36.7928 37.5304C36.1403 37.524 35.5234 37.4003 35.0044 36.9946C34.456 36.3223 34.2454 35.8011 34.3428 34.939C34.5264 34.3616 34.8225 33.9069 35.3391 33.5581C35.8691 33.35 36.3547 33.3147 36.92 33.3165Z"
          fill={props.color || "#00C3F7"}
        />
        <path
          d="M24.8716 16.9395C24.9588 16.9389 25.0461 16.9383 25.1359 16.9377C25.4228 16.936 25.7096 16.9361 25.9964 16.9364C26.1969 16.9359 26.3973 16.9354 26.5978 16.9348C27.0174 16.934 27.4371 16.934 27.8568 16.9346C28.393 16.9353 28.9292 16.9333 29.4655 16.9305C29.8793 16.9287 30.2932 16.9286 30.7071 16.929C30.9047 16.929 31.1024 16.9283 31.3 16.9271C31.5771 16.9256 31.8541 16.9265 32.1311 16.9278C32.2118 16.9269 32.2924 16.926 32.3755 16.9251C33.0281 16.9322 33.645 17.0714 34.164 17.5278C34.7131 18.285 34.9216 18.8698 34.8256 19.8404C34.6332 20.5611 34.2888 20.9881 33.7186 21.3939C33.2377 21.6552 32.7885 21.6676 32.261 21.6657C32.1714 21.6663 32.0819 21.6669 31.9897 21.6676C31.6945 21.6692 31.3994 21.6691 31.1042 21.6688C30.8984 21.6693 30.6925 21.6698 30.4867 21.6704C30.0555 21.6713 29.6243 21.6712 29.193 21.6706C28.6415 21.6699 28.09 21.6719 27.5385 21.6747C27.1134 21.6765 26.6884 21.6766 26.2633 21.6762C26.06 21.6763 25.8567 21.6769 25.6534 21.6781C25.3686 21.6796 25.0839 21.6788 24.7991 21.6774C24.7158 21.6783 24.6325 21.6792 24.5467 21.6801C23.8876 21.6731 23.3567 21.5289 22.8501 21.0428C22.7882 20.9615 22.7263 20.8802 22.6624 20.7964C22.5988 20.715 22.5352 20.6337 22.4696 20.5499C22.115 20.0135 22.1442 19.3963 22.2058 18.7649C22.4039 18.0584 22.7543 17.6213 23.3128 17.2113C23.8348 16.9777 24.3137 16.9375 24.8716 16.9395Z"
          fill={props.color || "#00FF85"}
        />
        <path
          d="M16.5396 25.4028C16.6734 25.402 16.6734 25.402 16.81 25.4012C17.1055 25.3996 17.401 25.3989 17.6965 25.3983C17.9019 25.3976 18.1073 25.397 18.3127 25.3964C18.7436 25.3952 19.1744 25.3946 19.6053 25.3942C20.157 25.3936 20.7086 25.391 21.2602 25.3879C21.6846 25.3859 22.109 25.3853 22.5334 25.3851C22.7367 25.3848 22.94 25.3839 23.1434 25.3825C23.4281 25.3806 23.7129 25.3808 23.9976 25.3816C24.0813 25.3805 24.165 25.3795 24.2513 25.3785C24.8467 25.3831 25.2867 25.5074 25.7463 25.9234C26.2165 26.4996 26.4328 26.9431 26.3892 27.7099C26.3047 28.3745 26.0311 28.7796 25.5453 29.2158C25.0504 29.5937 24.6323 29.58 24.0389 29.5809C23.9497 29.5814 23.8604 29.582 23.7685 29.5825C23.473 29.5841 23.1775 29.5849 22.882 29.5855C22.6766 29.5861 22.4712 29.5867 22.2658 29.5874C21.8349 29.5885 21.4041 29.5891 20.9732 29.5895C20.4215 29.5901 19.8699 29.5927 19.3183 29.5958C18.8939 29.5979 18.4695 29.5984 18.0451 29.5986C17.8418 29.5989 17.6384 29.5998 17.4351 29.6012C17.1503 29.6031 16.8656 29.6029 16.5809 29.6022C16.4972 29.6032 16.4135 29.6042 16.3272 29.6052C15.7287 29.6006 15.2824 29.4783 14.8284 29.0489C14.7776 28.9848 14.7267 28.9207 14.6743 28.8547C14.6221 28.7906 14.5699 28.7265 14.5161 28.6605C14.1569 28.1325 14.2402 27.4513 14.2823 26.8377C14.4648 26.2433 14.856 25.8796 15.3455 25.5294C15.7465 25.3924 16.1218 25.4035 16.5396 25.4028Z"
          fill={props.color || "#00C3F7"}
        />
        <path
          d="M4.75066 33.3237C4.91851 33.3225 4.91851 33.3225 5.08975 33.3213C5.32619 33.3201 5.56263 33.3195 5.79907 33.3196C6.15908 33.319 6.51893 33.3147 6.87891 33.3102C7.10917 33.3095 7.33944 33.309 7.5697 33.3088C7.67653 33.3071 7.78336 33.3054 7.89343 33.3036C8.63889 33.309 9.25421 33.4452 9.81504 34.0068C10.3081 34.608 10.4713 35.1499 10.4519 35.9379C10.3733 36.5738 10.1002 37.0817 9.66121 37.5085C8.97577 38.012 8.34759 38.0366 7.54478 38.0387C7.43818 38.0395 7.33158 38.0403 7.22175 38.0411C6.99679 38.0423 6.77182 38.0428 6.54686 38.0428C6.20497 38.0433 5.86324 38.0476 5.52139 38.0521C5.30201 38.0528 5.08263 38.0533 4.86325 38.0535C4.71159 38.0561 4.71159 38.0561 4.55687 38.0587C3.76756 38.0528 3.08245 37.8663 2.47566 37.2973C1.99572 36.5276 1.92913 35.8214 2.05667 34.9087C2.26285 34.231 2.69626 33.8062 3.26731 33.4724C3.7595 33.3099 4.23867 33.3249 4.75066 33.3237Z"
          fill={props.color || "#00FF85"}
        />
        <path
          d="M41.4541 16.9359C41.5587 16.9354 41.6634 16.9348 41.7713 16.9343C41.9921 16.9336 42.2129 16.9336 42.4337 16.9341C42.7697 16.9345 43.1056 16.9317 43.4415 16.9286C43.6568 16.9284 43.8721 16.9283 44.0873 16.9284C44.1869 16.9273 44.2864 16.9262 44.3889 16.9251C45.0992 16.9306 45.747 17.0237 46.3173 17.5261C46.8945 18.3233 47.0693 18.9335 46.9765 19.9533C46.7767 20.6421 46.3014 21.0984 45.7633 21.5038C45.4415 21.6132 45.1464 21.6397 44.8102 21.6441C44.6588 21.6464 44.6588 21.6464 44.5044 21.6488C44.3961 21.6497 44.2877 21.6505 44.1761 21.6514C44.0076 21.6529 44.0076 21.6529 43.8356 21.6544C43.5979 21.656 43.3602 21.6573 43.1225 21.6582C42.8801 21.6596 42.6378 21.662 42.3954 21.6656C42.0445 21.6707 41.6938 21.6726 41.3429 21.6738C41.2354 21.6759 41.128 21.678 41.0173 21.6801C40.2232 21.6774 39.617 21.5073 39.025 20.9149C38.555 20.1695 38.4788 19.5314 38.5942 18.6414C38.7808 18.0004 39.1806 17.5703 39.6971 17.2103C40.2861 16.9402 40.8255 16.9352 41.4541 16.9359Z"
          fill={props.color || "#FFA42C"}
        />
        <path
          d="M4.01526 25.4322C4.12233 25.4301 4.2294 25.428 4.33972 25.4258C4.83046 25.4196 5.32119 25.4143 5.81197 25.411C6.07015 25.4086 6.32833 25.4049 6.58647 25.3999C6.95987 25.3926 7.33308 25.3897 7.70654 25.3876C7.87806 25.3831 7.87806 25.3831 8.05305 25.3785C8.86362 25.3807 9.37805 25.5744 9.98852 26.1027C10.4246 26.6637 10.5024 27.2426 10.4293 27.9387C10.21 28.6061 9.87472 28.9709 9.33149 29.4091C8.81749 29.6388 8.31959 29.6048 7.7612 29.6017C7.5813 29.6021 7.5813 29.6021 7.39776 29.6026C7.14447 29.6028 6.89117 29.6022 6.63788 29.6009C6.25097 29.5992 5.86418 29.6009 5.47728 29.6029C5.23087 29.6027 4.98447 29.6023 4.73806 29.6017C4.6227 29.6023 4.50733 29.603 4.38847 29.6037C3.68211 29.597 3.15812 29.5405 2.62135 29.0452C2.56866 28.9808 2.51597 28.9163 2.46167 28.8499C2.40756 28.7852 2.35345 28.7204 2.29769 28.6537C1.88965 28.1131 2.00239 27.3923 2.05538 26.7507C2.30358 26.2173 2.68506 25.9073 3.1572 25.5627C3.47411 25.4592 3.68531 25.4382 4.01526 25.4322Z"
          fill={props.color || "#FFA42C"}
        />
      </g>
      <defs>
        <clipPath id="clip0_9_21">
          <rect width="48" height="48" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const OpenTelemetryLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M24.8395 25.6286C22.9132 27.5563 22.9132 30.6804 24.8395 32.6081C26.7672 34.5344 29.8913 34.5344 31.8175 32.6081C33.7453 30.6804 33.7453 27.5563 31.8175 25.6286C29.8913 23.7023 26.7672 23.7023 24.8395 25.6286ZM30.0561 30.8496C29.8294 31.0767 29.5601 31.2569 29.2636 31.3798C28.9672 31.5027 28.6494 31.566 28.3285 31.566C28.0076 31.566 27.6898 31.5027 27.3934 31.3798C27.0969 31.2569 26.8276 31.0767 26.6009 30.8496C26.374 30.6229 26.194 30.3536 26.0712 30.0573C25.9485 29.761 25.8852 29.4433 25.8852 29.1226C25.8852 28.8018 25.9485 28.4842 26.0712 28.1878C26.194 27.8915 26.374 27.6223 26.6009 27.3955C26.8276 27.1684 27.0969 26.9883 27.3934 26.8653C27.6898 26.7424 28.0076 26.6791 28.3285 26.6791C28.6494 26.6791 28.9672 26.7424 29.2636 26.8653C29.5601 26.9883 29.8294 27.1684 30.0561 27.3955C31.0101 28.345 31.0101 29.8957 30.0561 30.8496ZM31.736 1.34831L28.7122 4.37174C28.4276 4.65855 28.2678 5.04626 28.2678 5.45035C28.2678 5.85444 28.4276 6.24215 28.7122 6.52896L40.5169 18.3337C40.8037 18.6183 41.1915 18.7781 41.5955 18.7781C41.9996 18.7781 42.3873 18.6183 42.6742 18.3337L45.6976 15.3099C46.2888 14.7187 46.2888 13.7453 45.6976 13.1545L33.8877 1.34795C33.6017 1.06399 33.2149 0.904633 32.8119 0.904633C32.4088 0.904633 32.0221 1.06435 31.736 1.34831ZM10.5522 39.9849C10.81 39.7259 10.9548 39.3754 10.9548 39.01C10.9548 38.6446 10.81 38.2941 10.5522 38.0351L9.01628 36.4977C8.75705 36.24 8.40638 36.0954 8.04085 36.0954C7.67532 36.0954 7.32464 36.24 7.06541 36.4977L3.89145 39.6735L3.88557 39.6779L3.01497 38.8058C2.53285 38.3237 1.74817 38.3237 1.27083 38.8058C0.788711 39.2876 0.788711 40.0723 1.27083 40.55L6.50178 45.7795C6.61614 45.894 6.75196 45.9849 6.90147 46.0469C7.05098 46.1089 7.21126 46.1408 7.37312 46.1408C7.53498 46.1408 7.69525 46.1089 7.84477 46.0469C7.99428 45.9849 8.1301 45.894 8.24445 45.7795C8.7218 45.2974 8.72657 44.5127 8.24445 44.0368L7.37238 43.1647L7.37826 43.1603L10.5522 39.9849Z"
        fill={props.color || "#F5A800"}
      />
      <path
        d="M26.5465 10.029L19.8296 16.7456C19.2329 17.3423 19.2329 18.3249 19.8296 18.9216L23.9773 23.0682C26.909 20.9583 31.02 21.2194 33.6575 23.8588L37.0165 20.4994C37.6121 19.9027 37.6121 18.9216 37.0165 18.3249L28.7214 10.029C28.5787 9.88612 28.4092 9.77276 28.2226 9.69543C28.0361 9.61809 27.8361 9.57828 27.6342 9.57828C27.4322 9.57828 27.2322 9.61809 27.0457 9.69543C26.8591 9.77276 26.6893 9.88612 26.5465 10.029ZM22.2332 24.8164L19.7833 22.3665C19.2094 21.793 18.2687 21.793 17.6962 22.3665L9.0563 31.0101C8.7807 31.2877 8.62604 31.663 8.62604 32.0542C8.62604 32.4454 8.7807 32.8207 9.0563 33.0983L13.9513 37.9936C14.2289 38.2694 14.6043 38.4241 14.9956 38.4241C15.3868 38.4241 15.7622 38.2694 16.0398 37.9936L21.5961 32.4282C20.4215 30.0014 20.6322 27.0698 22.2332 24.8164Z"
        fill={props.color || "#425CC7"}
      />
    </svg>
  );
};

export const LlamaIndexLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <defs>
        <linearGradient
          id="paint0_linear_9_37"
          x1="8.85215"
          y1="5.7875"
          x2="47.4621"
          y2="38.1444"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.062" stopColor="#F6DCD9" />
          <stop offset="0.326" stopColor="#FFA5EA" />
          <stop offset="0.589" stopColor="#45DFF8" />
          <stop offset="1" stopColor="#BC8DEB" />
        </linearGradient>
      </defs>
      <path
        d="M31.0409 34.1037C27.1184 35.8362 22.8696 35.1256 21.2346 34.5537C21.2346 34.9475 21.2159 36.1606 21.1446 37.8913C21.0734 39.6238 20.5221 40.7169 20.254 41.0488C20.284 42.1306 20.3084 44.4931 20.1659 45.2863C20.0292 45.9278 19.7207 46.5202 19.2734 47H16.8659C17.0796 45.9181 17.9065 45.2262 18.2928 45.0162C18.5065 42.7794 18.0865 40.8369 17.8465 40.1469C17.6103 40.9887 17.0271 42.9406 16.5996 44.0244C16.2496 44.8851 15.8009 45.7025 15.2628 46.46H13.4796C13.3896 45.3762 13.9859 45.0162 14.3703 45.0162C14.5484 44.6844 14.9759 43.6456 15.2628 42.1288C15.5478 40.6156 15.1428 37.7712 14.9065 36.5394V32.6619C12.0528 31.1281 10.984 29.5944 10.2696 27.8806C9.69965 26.5119 9.8534 24.425 10.0034 23.5531C9.8234 23.2231 9.30402 22.3812 9.1109 21.2094C8.84465 19.5856 8.99277 18.4137 9.1109 17.7819C8.93277 17.6019 8.57652 16.6813 8.57652 14.4444C8.57652 12.2094 9.2309 11.0487 9.55715 10.7469V9.755C8.3084 9.665 7.06152 9.125 6.34715 8.40312C5.63465 7.68125 6.16902 6.59937 6.61527 6.23937C7.06152 5.8775 7.5059 6.1475 8.13027 5.9675C8.75465 5.7875 9.28902 5.6075 9.55527 5.0675C9.77277 4.63063 9.3509 2.84 9.11277 2C10.1815 2.14437 10.8659 3.08188 11.074 3.53375V2C12.4109 2.63188 14.8184 4.16375 15.6209 7.50125C16.2621 10.1713 16.7196 15.7681 16.8678 18.2319C20.2859 18.2619 24.6246 17.7444 28.5471 18.5938C32.1115 19.3663 33.7165 20.9375 35.5878 20.9375C37.4609 20.9375 38.5315 19.8556 39.8665 20.7575C41.2053 21.6594 41.9178 24.185 41.7396 26.0788C41.5971 27.5938 40.4328 28.0925 39.8684 28.1525C39.1559 30.5337 39.8684 32.8119 40.3128 33.6537V37.0794C40.5209 37.3794 40.9371 38.3075 40.9371 39.605C40.9371 40.9044 40.5209 41.7687 40.3128 42.0406C40.669 44.06 40.1628 46.1281 39.8665 46.91H37.4609C37.7459 46.1881 38.2334 46.0081 38.4415 46.0081C38.869 43.7713 38.5596 41.7087 38.3515 40.9587C36.9978 40.1637 36.124 38.765 35.8559 38.1631C35.8859 38.6731 35.8015 40.0925 35.2315 41.6788C34.6615 43.2669 33.8065 44.2044 33.4503 44.4744V46.3681H31.0428C31.0428 45.215 31.6953 44.9862 32.0234 45.0162C32.4396 44.2662 33.4484 43.1225 33.4484 40.8687C33.4484 38.9656 32.1115 38.0713 31.1309 36.3594C30.6659 35.5456 30.8928 34.5256 31.0428 34.1037H31.0409Z"
        fill={props.color || "url(#paint0_linear_9_37)"}
      />
    </svg>
  );
};

export const OpenrouterLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M32.8672 5.25977L46.0869 12.8896L46.5371 13.1494L46.0869 13.4092L32.8672 21.0391L32.417 21.2988V16.8164C27.8539 16.6215 25.2622 16.6357 21.2783 19.459L21.2764 19.46C16.8994 22.5588 16.0207 23.1897 14.3027 24.1855C15.6096 24.9541 16.5267 25.5872 19.2432 27.5088L21.0117 28.7607L21.0137 28.7617C25.0022 31.5883 27.5954 31.598 32.168 31.4023V26.9219L32.6182 27.1816L45.8379 34.8115L46.2881 35.0713L45.8379 35.3311L32.6182 42.9609L32.168 43.2207V39.1943C27.2907 39.3832 22.2608 39.1222 16.5439 35.0713C11.2145 31.2981 11.3614 31.3909 8.97363 30.0479C8.11283 29.5637 6.59667 29.0209 4.96094 28.5859C4.18242 28.3789 3.45452 28.2168 2.87207 28.1094C2.21137 27.9875 1.95333 27.9766 2.00684 27.9766V20.2441C2.09106 20.2441 2.1797 20.2461 2.27148 20.25V20.2441C2.21614 20.2441 2.47352 20.2335 3.13574 20.1113C3.71815 20.0039 4.44615 19.8417 5.22461 19.6348C6.86061 19.1998 8.3775 18.657 9.23828 18.1729C11.3275 16.9977 11.476 16.922 15.0508 14.3936L16.8086 13.1494C22.5195 9.10274 27.5445 8.83763 32.417 9.02539V5L32.8672 5.25977Z"
        fill={props.color || "#95A5B9"}
      />
    </svg>
  );
};

export const MastraLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M29.3135 20.1104L26.2529 23.1709H33.1367V24.9883H26.252L29.3115 28.0479L28.0264 29.333L24.0576 25.3643L20.0898 29.333L18.8047 28.0479L21.8643 24.9883H14.9814V23.1709H21.8633L18.8027 20.1104L20.0879 18.8242L24.0576 22.7939L28.0283 18.8242L29.3135 20.1104Z"
        fill="currentColor"
      />
      <path
        d="M24 1C36.7025 1.00007 47 11.2975 47 24C46.9999 36.7024 36.7024 46.9999 24 47C11.2975 47 1.00007 36.7025 1 24C1 11.2975 11.2975 1 24 1ZM4.56445 32.4326C7.82403 39.9348 15.2989 45.1816 24 45.1816C26.7482 45.1816 29.3737 44.6576 31.7832 43.7051C30.3847 43.7578 28.9324 43.6355 27.4619 43.3457C24.2466 42.712 20.8982 41.2831 17.7295 39.1357C13.9714 38.4133 10.5942 37.0566 7.87305 35.2314C6.62912 34.3971 5.51565 33.458 4.56445 32.4326ZM41.5713 34.167C41.1123 34.5378 40.6306 34.8936 40.127 35.2314C35.9602 38.0262 30.2557 39.7256 24 39.7256C23.4287 39.7256 22.8621 39.7105 22.3008 39.6826C24.1546 40.5767 26.0153 41.2071 27.8135 41.5615C32.3115 42.448 36.3076 41.6018 38.9219 39.0312C38.9627 38.9907 39.0034 38.9501 39.0439 38.9092C40.3011 37.6255 41.1438 36.0107 41.5713 34.167ZM24 10.0918C18.0554 10.0918 12.7143 11.7096 8.88574 14.2773C7.83239 14.9838 6.90013 15.7575 6.10156 16.584C6.08163 17.7341 6.19296 18.9418 6.43848 20.1875C7.32997 24.7103 9.96272 29.6306 14.166 33.834C15.5278 35.1958 16.966 36.3917 18.4404 37.418C20.2097 37.7352 22.073 37.9072 24 37.9072C29.9446 37.9072 35.2857 36.2895 39.1143 33.7217C40.1681 33.0149 41.0996 32.24 41.8984 31.4131C41.9182 30.2638 41.8068 29.0571 41.5615 27.8125C40.67 23.2898 38.0373 18.3693 33.834 14.166C32.4716 12.8037 31.0337 11.6066 29.5586 10.5801C27.7897 10.263 25.9267 10.0918 24 10.0918ZM4.38477 18.7637C3.36589 20.3979 2.81836 22.1696 2.81836 24C2.81849 27.6953 5.05148 31.15 8.88574 33.7217C10.4077 34.7425 12.1694 35.6115 14.1123 36.29C13.6959 35.913 13.2846 35.5229 12.8809 35.1191C8.45754 30.6958 5.62453 25.4614 4.6543 20.5391C4.53694 19.9436 4.44713 19.3508 4.38477 18.7637ZM33.8877 11.708C34.3046 12.0854 34.7159 12.4767 35.1201 12.8809C39.5433 17.3042 42.3765 22.5386 43.3467 27.4609C43.4638 28.0554 43.5529 28.6472 43.6152 29.2334C44.6334 27.5998 45.1816 25.8296 45.1816 24C45.1816 20.3045 42.9486 16.8491 39.1143 14.2773C37.5922 13.2565 35.8307 12.3866 33.8877 11.708ZM24 2.81836C21.2516 2.81836 18.6254 3.34131 16.2158 4.29395C17.6149 4.24095 19.0679 4.36435 20.5391 4.6543C23.7533 5.28784 27.1008 6.71523 30.2686 8.86133C34.0275 9.58371 37.4052 10.9421 40.127 12.7676C41.3705 13.6016 42.4836 14.5405 43.4346 15.5654C40.1745 8.06434 32.7004 2.81841 24 2.81836ZM20.1875 6.43848C15.6578 5.54571 11.6356 6.40959 9.02246 9.02246C7.7289 10.3161 6.86377 11.9552 6.42871 13.8301C6.88738 13.4596 7.36974 13.1052 7.87305 12.7676C12.0398 9.9729 17.7442 8.27344 24 8.27344C24.5706 8.27344 25.1366 8.28762 25.6973 8.31543C23.8445 7.42208 21.9846 6.79271 20.1875 6.43848Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const AutogenLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 96 85"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect width="96" height="85" rx="6" fill="#2D2D2F" />
      <path
        d="M32.6484 28.7109L23.3672 57H15.8906L28.5703 22.875H33.3281L32.6484 28.7109ZM40.3594 57L31.0547 28.7109L30.3047 22.875H35.1094L47.8594 57H40.3594ZM39.9375 44.2969V49.8047H21.9141V44.2969H39.9375ZM77.6484 39.1641V52.6875C77.1172 53.3281 76.2969 54.0234 75.1875 54.7734C74.0781 55.5078 72.6484 56.1406 70.8984 56.6719C69.1484 57.2031 67.0312 57.4688 64.5469 57.4688C62.3438 57.4688 60.3359 57.1094 58.5234 56.3906C56.7109 55.6562 55.1484 54.5859 53.8359 53.1797C52.5391 51.7734 51.5391 50.0547 50.8359 48.0234C50.1328 45.9766 49.7812 43.6406 49.7812 41.0156V38.8828C49.7812 36.2578 50.1172 33.9219 50.7891 31.875C51.4766 29.8281 52.4531 28.1016 53.7188 26.6953C54.9844 25.2891 56.4922 24.2188 58.2422 23.4844C59.9922 22.75 61.9375 22.3828 64.0781 22.3828C67.0469 22.3828 69.4844 22.8672 71.3906 23.8359C73.2969 24.7891 74.75 26.1172 75.75 27.8203C76.7656 29.5078 77.3906 31.4453 77.625 33.6328H70.8047C70.6328 32.4766 70.3047 31.4688 69.8203 30.6094C69.3359 29.75 68.6406 29.0781 67.7344 28.5938C66.8438 28.1094 65.6875 27.8672 64.2656 27.8672C63.0938 27.8672 62.0469 28.1094 61.125 28.5938C60.2188 29.0625 59.4531 29.7578 58.8281 30.6797C58.2031 31.6016 57.7266 32.7422 57.3984 34.1016C57.0703 35.4609 56.9062 37.0391 56.9062 38.8359V41.0156C56.9062 42.7969 57.0781 44.375 57.4219 45.75C57.7656 47.1094 58.2734 48.2578 58.9453 49.1953C59.6328 50.1172 60.4766 50.8125 61.4766 51.2812C62.4766 51.75 63.6406 51.9844 64.9688 51.9844C66.0781 51.9844 67 51.8906 67.7344 51.7031C68.4844 51.5156 69.0859 51.2891 69.5391 51.0234C70.0078 50.7422 70.3672 50.4766 70.6172 50.2266V44.1797H64.1953V39.1641H77.6484Z"
        fill="white"
      />
    </svg>
  );
};

export const LangchainLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M16.746 29.504C16.772 29.384 16.794 29.268 16.822 29.164L16.944 29.454C17.174 30.014 17.402 30.568 17.956 30.882C17.932 31.39 17.288 31.596 16.852 31.534C16.756 31.306 16.622 31.078 16.342 31.206C16.056 31.318 15.742 31.186 15.81 30.836C16.476 30.812 16.624 30.094 16.746 29.504ZM36.77 18.99C36.134 18.99 35.538 19.234 35.092 19.674L33.288 21.448C32.802 21.928 32.552 22.592 32.602 23.274L32.614 23.386C32.678 23.91 32.912 24.382 33.288 24.75C33.548 25.006 33.834 25.17 34.182 25.282C34.2336 25.5617 34.2155 25.8497 34.1293 26.1208C34.0432 26.3918 33.8916 26.6374 33.688 26.836L33.576 26.946C32.8868 26.7185 32.2715 26.3097 31.7946 25.7625C31.3178 25.2153 30.997 24.5499 30.866 23.836L30.846 23.722L30.754 23.796C30.694 23.844 30.634 23.896 30.578 23.952L28.774 25.726C28.5547 25.9412 28.3805 26.198 28.2616 26.4813C28.1427 26.7646 28.0815 27.0687 28.0815 27.376C28.0815 27.6832 28.1427 27.9874 28.2616 28.2707C28.3805 28.554 28.5547 28.8108 28.774 29.026C29.236 29.482 29.844 29.71 30.454 29.71C31.062 29.71 31.668 29.482 32.13 29.028L33.934 27.252C34.2165 26.9758 34.4235 26.6317 34.5352 26.2528C34.647 25.8738 34.6597 25.4725 34.5723 25.0872C34.4848 24.7018 34.3001 24.3454 34.0357 24.0518C33.7713 23.7582 33.4361 23.5372 33.062 23.41C33.0108 23.1056 33.0342 22.7933 33.1304 22.4999C33.2265 22.2066 33.3925 21.941 33.614 21.726C34.3088 21.9586 34.929 22.3721 35.4109 22.924C35.8929 23.4759 36.2191 24.1462 36.356 24.866L36.376 24.98L36.47 24.906C36.53 24.858 36.59 24.806 36.646 24.75L38.45 22.974C38.6695 22.7589 38.8439 22.5022 38.9629 22.2189C39.082 21.9355 39.1433 21.6313 39.1433 21.324C39.1433 21.0167 39.082 20.7124 38.9629 20.4291C38.8439 20.1458 38.6695 19.8891 38.45 19.674C38.0025 19.2328 37.3984 18.9868 36.77 18.99Z"
        fill={props.color || "#1C3C3C"}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M35.802 12.5H12.2C5.472 12.5 0 17.884 0 24.5C0 31.116 5.472 36.5 12.198 36.5H35.8C42.528 36.5 48 31.116 48 24.5C48 17.884 42.528 12.5 35.802 12.5ZM24.16 31.314C23.77 31.394 23.332 31.408 23.036 31.102C22.946 31.302 22.764 31.256 22.594 31.214C22.5537 31.2031 22.513 31.1937 22.472 31.186C22.452 31.236 22.438 31.282 22.42 31.332C21.762 31.374 21.27 30.714 20.956 30.216C20.6481 30.0597 20.3324 29.9195 20.01 29.796C19.666 29.656 19.32 29.516 18.992 29.336C18.9848 29.4512 18.9822 29.5666 18.984 29.682C18.98 30.17 18.976 30.688 18.53 30.984C18.516 31.574 19.002 31.568 19.482 31.564C19.896 31.558 20.302 31.554 20.376 31.932C20.3428 31.9357 20.3094 31.9377 20.276 31.938C20.184 31.938 20.092 31.938 20.022 32.006C19.788 32.228 19.538 32.132 19.278 32.032C19.038 31.94 18.792 31.844 18.544 31.992C18.3628 32.0831 18.1877 32.186 18.02 32.3C17.7029 32.5383 17.3204 32.6738 16.924 32.688C16.876 32.616 16.896 32.57 16.936 32.528C16.967 32.4925 16.9957 32.4551 17.022 32.416C17.06 32.36 17.092 32.302 17.124 32.248C17.232 32.058 17.33 31.888 17.608 31.808C17.238 31.75 16.92 31.918 16.608 32.082L16.6 32.086C16.5569 32.109 16.5135 32.1317 16.47 32.154C16.276 32.234 16.162 32.172 16.046 32.108C15.882 32.018 15.71 31.924 15.294 32.188C15.214 32.124 15.254 32.066 15.298 32.016C15.48 31.798 15.718 31.766 15.988 31.778C15.286 31.392 14.78 31.666 14.368 31.888C14.004 32.084 13.714 32.24 13.426 31.864C13.296 31.898 13.222 31.99 13.15 32.08C13.12 32.12 13.09 32.156 13.056 32.19C12.986 32.112 13.002 32.024 13.02 31.934L13.03 31.882C13.0332 31.8621 13.0352 31.8421 13.036 31.822L12.982 31.802C12.876 31.758 12.772 31.714 12.802 31.554C12.568 31.474 12.402 31.614 12.23 31.742C12.122 31.66 12.21 31.552 12.294 31.452C12.33 31.4132 12.3603 31.3694 12.384 31.322C12.46 31.192 12.59 31.188 12.716 31.184C12.824 31.182 12.932 31.178 13.006 31.1C13.272 30.95 13.6 31.028 13.93 31.106C14.172 31.162 14.414 31.22 14.638 31.19C15.044 31.24 15.546 30.83 15.342 30.42C14.97 29.954 14.974 29.364 14.976 28.794V28.508C14.944 28.292 14.632 28.042 14.32 27.792C14.08 27.602 13.84 27.41 13.724 27.232C13.404 26.878 13.154 26.468 12.906 26.062L12.876 26.014C12.452 25.206 12.282 24.294 12.112 23.384C11.906 22.292 11.702 21.204 11.06 20.304C10.528 20.592 9.836 20.454 9.378 20.068C9.138 20.282 9.118 20.562 9.102 20.86L9.1 20.888C8.506 20.304 8.58 19.2 9.054 18.548C9.248 18.292 9.48 18.082 9.738 17.896C9.798 17.854 9.818 17.812 9.816 17.748C10.286 15.668 13.488 16.07 14.5 17.542C14.834 17.954 15.062 18.426 15.29 18.898C15.564 19.464 15.836 20.03 16.29 20.488C16.73 20.962 17.194 21.414 17.658 21.866C18.376 22.566 19.094 23.264 19.722 24.044C20.702 25.218 21.4 26.596 22.01 27.984C22.11 28.168 22.17 28.37 22.23 28.57C22.318 28.87 22.408 29.168 22.63 29.404C22.682 29.474 22.798 29.58 22.928 29.7C23.24 29.986 23.642 30.356 23.506 30.518C23.524 30.556 23.56 30.598 23.606 30.638C23.67 30.694 23.754 30.754 23.838 30.814C24.082 30.988 24.34 31.17 24.16 31.314ZM39.716 24.224L37.912 25.998C37.432 26.472 36.838 26.824 36.194 27.018L36.16 27.028L36.148 27.058C35.9395 27.6013 35.6161 28.0932 35.2 28.5L33.396 30.276C32.61 31.05 31.564 31.476 30.448 31.476C29.334 31.476 28.288 31.05 27.5 30.276C27.1142 29.898 26.8077 29.4468 26.5984 28.9488C26.3892 28.4508 26.2814 27.9161 26.2814 27.376C26.2814 26.8359 26.3892 26.3011 26.5984 25.8032C26.8077 25.3052 27.1142 24.854 27.5 24.476L29.304 22.7C29.788 22.224 30.366 21.882 31.022 21.684L31.054 21.676L31.066 21.644C31.276 21.1 31.596 20.612 32.016 20.196L33.82 18.422C34.606 17.648 35.654 17.222 36.768 17.222C37.884 17.222 38.928 17.648 39.716 18.422C40.504 19.196 40.936 20.226 40.936 21.322C40.936 22.42 40.504 23.45 39.716 24.222V24.224Z"
        fill={props.color || "#1C3C3C"}
      />
    </svg>
  );
};

export const LangfuseLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  if (props.color) {
    return (
      <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
        <path
          d="M15.2422 10.1C18.1741 10.094 20.498 11.1403 22.002 12.0863L22.1621 12.1879L21.7637 12.514C20.1278 13.821 18.3575 14.9504 16.4824 15.8822L16.2217 16.0043L15.6104 16.2758L14.998 16.5258C14.5601 16.6837 14.1261 16.8758 13.7002 17.1117L13.4443 17.2523C12.2223 17.9459 11.1902 18.9302 10.4404 20.1185C9.69063 21.3069 9.24542 22.6623 9.14551 24.0638L9.12598 24.4838V25.0004C9.14029 25.3604 9.15739 25.7207 9.17773 26.0804C9.1817 27.0186 9.40132 27.9432 9.81836 28.7836L9.99805 29.1195C10.1517 29.3834 10.3245 29.6363 10.5156 29.8744C10.9095 30.3862 11.3618 30.8059 11.8496 31.1459L12.3438 31.4681C13.2208 32.027 14.1787 32.448 15.1836 32.7162L16.7139 33.0863C17.4098 33.2543 18.0099 33.4139 18.6299 33.6058C20.2739 34.1058 21.8702 34.7559 23.5342 35.6459L23.8584 35.9183C22.7424 36.8083 19.3699 39.142 14.6699 38.722C12.794 38.5536 10.9799 37.9652 9.3623 37.0004C8.08089 36.237 6.95829 35.2332 6.05762 34.0443L5.7959 33.64C4.67624 31.9331 3.91106 30.0186 3.5459 28.0101L3.41016 27.2562L3.34375 27.2855C3.02412 25.3651 3.00426 23.4065 3.28418 21.4799L3.33594 21.5316L3.52832 20.14C3.5883 19.8661 3.65176 19.6001 3.71973 19.3402H3.71777L3.87012 18.7982C4.25418 17.4973 4.8478 16.2672 5.62793 15.1576L6.20215 14.4076C8.20614 11.9798 11.0965 10.3841 14.3164 10.1361L14.6504 10.1156L15.2422 10.1ZM13.334 26.4496C17.298 26.4736 19.6203 26.7662 21.3623 27.5502C22.2903 27.966 23.1624 28.497 23.958 29.1302L24.8076 29.85C26.8115 31.4879 29.2118 32.4036 31.8477 31.9037C32.2629 31.825 32.6731 31.7194 33.0742 31.5863C34.7344 31.3591 36.4234 31.4685 38.04 31.9095L38.6738 32.098C39.6548 32.4147 40.5927 32.8519 41.4658 33.3998L41.9336 33.7103L41.624 34.0844C39.6 36.3982 36.8016 37.8484 33.7637 38.0844L33.4277 38.1039C30.59 38.2478 28.0723 37.4158 25.7725 35.848L25.8096 35.8138L25.1963 35.3676C24.8224 35.0977 24.4743 34.8202 24.1504 34.5443L24.0996 34.5023L23.4141 34.1478C20.9341 32.8999 18.8141 32.26 17.2041 31.848L15.6162 31.4584L15.1699 31.3422L15.1318 31.4457L15.1641 31.3402L15.1416 31.3344C14.5877 31.1764 14.0915 30.9718 13.6416 30.7299L13.2119 30.4965L13.0322 30.392L12.5957 30.1156C12.2757 29.9096 11.7894 29.5441 11.3516 28.9603L11.1338 28.6136L10.9385 28.2377C10.7025 27.7377 10.5697 27.2421 10.5137 26.7582L10.4941 26.558L10.9336 26.516L11.3076 26.4896C11.6196 26.4696 11.934 26.4556 12.29 26.4496H13.334ZM2.45605 28.9857C2.61603 29.6396 2.80012 30.2538 2.99609 30.8197L3.17578 31.3177C3.50366 32.1895 3.91939 33.0259 4.41602 33.8138L1.35645 35.8275C1.29645 35.8715 1.21582 35.8278 1.21582 35.7338V29.598L1.22363 29.5521L1.25391 29.5062L2.45605 28.9857ZM30.9424 26.1595C32.3063 26.0716 33.8344 26.104 35.4922 26.184L36.332 26.2279C36.8211 26.2551 37.3088 26.3021 37.7939 26.3695C40.2039 26.8614 42.5319 27.6938 44.708 28.8402L44.752 28.8597C45.3816 29.2399 45.9884 29.6573 46.5684 30.1097L46.7539 30.2582L46.7803 30.2904L46.79 30.3344V35.3158C46.7908 35.3357 46.7855 35.3559 46.7744 35.3724C46.7644 35.3869 46.7503 35.3981 46.7344 35.4056L46.6797 35.4164L46.6582 35.4095L46.6299 35.39L46.332 35.1361C45.4428 34.3898 44.4849 33.7283 43.4717 33.1615L43 32.9095L42.792 32.7562C41.4213 31.7724 39.8837 31.0442 38.2539 30.6078L37.752 30.4838C36.1818 30.128 34.5614 30.0488 32.9639 30.2484L32.6504 30.2904L32.25 30.4144L31.8936 30.5043L31.5283 30.5824C28.9803 31.0604 26.8318 29.8581 25.5518 28.8461L24.9561 28.35L25.3877 28.0199C25.9012 27.6453 26.4451 27.3139 27.0137 27.0297C28.0257 26.5257 29.3464 26.2615 30.9424 26.1595ZM44.6963 21.3539H44.6982L44.7744 21.7015L44.8037 21.9183C44.8575 22.4513 44.8896 22.9863 44.8984 23.5219L44.8936 24.3217C44.8716 25.1135 44.8044 25.8937 44.6885 26.6576L44.5703 27.3461C42.7076 26.3301 40.6912 25.6243 38.6016 25.2562L38.6621 24.9984C38.7572 24.5139 38.8109 24.0214 38.8223 23.5277C38.8322 23.2038 38.8182 22.9557 38.7822 22.6918L38.7695 22.6244C40.5267 22.3582 42.2479 21.888 43.8965 21.224L44.5957 20.9642L44.6963 21.3539ZM24.2617 12.0443C26.6016 10.1884 29.6978 8.92625 33.4336 9.26013C35.3063 9.42616 37.1184 10.0112 38.7344 10.972L39.1738 11.2504C39.9414 11.7803 40.6403 12.4033 41.2539 13.1058L41.6201 13.5463L41.1543 13.7357L40.7305 13.9799C40.3385 14.1959 39.9019 14.4098 39.4219 14.6078C39.1279 14.7178 38.8219 14.8222 38.502 14.9222C36.7052 15.484 34.8051 15.6338 32.9424 15.3617C31.0605 14.8857 29.0262 15.1938 27.2363 16.1537C26.4104 16.5846 25.6548 17.1395 24.9961 17.7982L24.332 18.4984L23.9678 18.8578L23.2002 19.5599C22.5487 20.0849 21.8422 20.5377 21.0938 20.9115C19.4398 21.7315 17.1196 21.9459 13.0596 21.7719L11.7881 21.7084L11.292 21.6742L11.0957 21.6537L11.3203 21.1957C11.607 20.6608 11.9577 20.1628 12.3643 19.7123C12.8821 19.1384 13.4358 18.6864 14.0156 18.3324C14.1402 18.2622 14.2671 18.1952 14.3955 18.1322L14.6543 18.0043C14.802 17.9348 14.9516 17.8696 15.1035 17.8099L15.3076 17.7338L15.3096 17.7318C16.4316 17.2938 17.5023 16.7644 18.4863 16.2084C20.6943 14.9844 22.6843 13.5079 23.9863 12.2679L24.2617 12.0443ZM46.7441 12.1488C46.7552 12.1564 46.7654 12.1667 46.7725 12.1781L46.7881 12.2318V18.3461L46.7764 18.3939L46.7422 18.4301C45.5599 19.1544 44.3111 19.7653 43.0137 20.2543C42.4898 20.4362 42.0557 20.5676 41.5479 20.6996C40.3139 21.0216 38.9941 21.3136 37.9541 21.4916L37.8623 21.5082C37.2923 21.5762 36.7018 21.62 36.0918 21.64L35.416 21.6517C31.832 21.6557 29.118 21.4879 27.126 20.7279L26.71 20.558C26.208 20.334 25.7238 20.0784 25.2578 19.7904L24.9756 19.6097C25.2234 19.3708 25.4642 19.1237 25.6963 18.8695L26.0117 18.5443C26.3357 18.2303 26.6761 17.9457 27.0361 17.6937C28.7221 16.5618 30.7379 16.1564 32.5918 16.6244L33.2158 16.7035C34.8998 16.8751 36.6015 16.7389 38.2363 16.3002L38.748 16.1537C39.3006 15.9798 39.8424 15.7726 40.3701 15.5336L40.834 15.3344C41.2661 15.1401 41.6872 14.922 42.0957 14.682L42.2676 14.6117C44.1396 13.8277 45.6098 12.8957 46.6318 12.1517C46.6425 12.1436 46.6549 12.1382 46.668 12.1351C46.681 12.132 46.6948 12.1317 46.708 12.1342C46.7209 12.1366 46.7333 12.1414 46.7441 12.1488ZM1.21582 12.7318C1.21592 12.6359 1.30438 12.594 1.36035 12.642L1.90039 13.1019C2.66362 13.7254 3.47615 14.2858 4.33008 14.7777L4.38574 14.8099L4.0957 15.2396C3.7597 15.7656 3.45355 16.3383 3.18555 16.9603L2.96582 17.5043C2.82987 17.8662 2.71418 18.2265 2.61621 18.5844L2.50391 19.0297L1.24805 17.806C1.22405 17.776 1.21777 17.7539 1.21777 17.7279V12.7318H1.21582Z"
          fill={props.color}
        />
      </svg>
    );
  } else {
    return (
      <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
        <path
          d="M13.1267 26.7672C16.7952 26.7614 19.6415 26.9435 21.7115 27.875C22.6449 28.2928 23.5208 28.8276 24.322 29.4658L24.7935 29.8607C25.9818 30.9129 28.4141 32.5728 31.4098 32.0073C31.7912 31.9345 32.1573 31.8387 32.5081 31.7198C34.1765 31.4763 35.8768 31.5691 37.5088 31.9928C39.1408 32.4164 40.6716 33.1623 42.0109 34.1866C39.9888 36.9562 36.8934 38.8038 33.3993 39.0741L33.0735 39.0933C28.9757 39.3003 25.8822 37.4085 23.8294 35.6528C20.1992 33.7055 17.3568 33.2148 15.6951 32.7913C14.7947 32.5478 13.9361 32.1705 13.1478 31.6719L12.6897 31.3748C12.0643 30.9387 11.5427 30.37 11.1622 29.7093C10.6811 28.8675 10.438 27.9109 10.4587 26.9416L11.0606 26.8688C11.7469 26.7963 12.4367 26.7624 13.1267 26.7672ZM44.1537 21.3219C44.3032 22.0503 44.4604 22.4068 44.5179 22.9473C44.7251 25.0151 44.5891 27.1031 44.1154 29.1266C41.958 27.9917 39.6493 27.1713 37.2595 26.6905C37.4818 26.0005 37.6045 25.2722 37.6237 24.5343C37.639 23.9497 37.5719 23.7312 37.4703 23.1658C38.5034 22.9913 39.7895 22.7058 40.9644 22.3991C42.2045 22.0733 43.1226 21.6957 44.1537 21.3219ZM23.5822 13.3409L23.8792 13.0975C24.9986 12.2082 28.3757 9.87751 33.0888 10.2992C35.2412 10.4908 37.0199 11.2 38.4152 12.0203C39.7224 12.7908 40.8302 13.9217 41.7426 15.1158C40.9184 15.7099 39.7013 16.3482 38.0702 16.8561C36.2638 17.4129 34.3565 17.5635 32.4851 17.2969C29.9302 16.6529 27.1203 17.5921 25.196 19.6947C24.1265 20.8619 22.8883 21.8586 21.4757 22.5582C19.3367 23.6181 16.0688 23.597 12.2642 23.3823C11.7795 23.3547 11.2961 23.3074 10.8152 23.2405C11.1579 22.2298 11.7024 21.2993 12.4157 20.5054C13.281 19.5468 14.3691 18.8161 15.5839 18.3779C19.5073 16.8561 22.7676 14.1153 23.5822 13.3409ZM6.06382 16.0051C4.8501 15.3707 3.71745 14.592 2.69049 13.6859C2.25732 13.3026 1.58265 13.615 1.58265 14.2015V18.9893C1.58202 19.0918 1.60423 19.1932 1.64766 19.286C1.69109 19.3788 1.75466 19.4608 1.83374 19.526L3.77149 21.412C4.03894 19.4373 4.83048 17.5703 6.06382 16.0051ZM2.64832 36.8009C2.54733 36.8739 2.428 36.9174 2.30371 36.9264C2.17941 36.9354 2.05507 36.9096 1.94462 36.8518C1.83417 36.7941 1.74198 36.7068 1.67839 36.5996C1.61479 36.4925 1.58231 36.3697 1.58457 36.2451V30.3667C1.58457 30.1884 1.65165 30.0198 1.76857 29.8933L1.87207 29.8032C1.88325 29.7956 1.89476 29.7886 1.90657 29.7821L3.82515 28.9464C4.19021 30.9475 4.95274 32.8551 6.06765 34.5565L2.64449 36.8009H2.64832Z"
          fill="#0A60B5"
        />
        <path
          d="M15.007 11.1061C18.848 10.9144 21.7192 12.5436 23.1988 13.6303C22.0776 14.6327 19.0454 17.0497 15.4613 18.445C15.1429 18.5602 14.8324 18.6959 14.5317 18.8513C13.2933 19.4679 12.247 20.4113 11.5058 21.5794C10.7647 22.7476 10.3569 24.0961 10.3265 25.4792C10.315 25.943 10.338 26.4049 10.3993 26.8592H10.3802C10.3687 27.3326 10.407 28.4442 11.0836 29.6077C11.6854 30.6465 12.4943 31.2023 12.8948 31.4438C13.0942 31.578 13.4123 31.7697 13.8053 31.9728C14.3762 32.279 14.9835 32.5121 15.6127 32.6667L16.2873 32.8315C17.997 33.2417 20.5903 33.8646 23.7681 35.5551C24.0901 35.8292 24.4351 36.1032 24.8069 36.3716C24.6919 36.4751 24.598 36.5632 24.5213 36.6361C23.8658 37.1977 20.2683 40.1647 15.007 39.6951C13.1237 39.5257 11.3026 38.9348 9.67868 37.9662C7.33076 36.5747 5.55593 34.3763 4.58801 31.8042C3.14395 27.972 2.99117 23.773 4.15293 19.8461C5.57701 15.0352 9.73043 11.5067 14.6793 11.1291L15.007 11.1061ZM41.8978 34.039C40.5565 33.0272 39.0279 32.2911 37.4005 31.8733C35.7731 31.4555 34.079 31.3643 32.4161 31.6048C32.0653 31.7198 31.6993 31.8157 31.3198 31.8866C28.324 32.4482 25.8956 30.8017 24.7073 29.7591C24.4763 29.5566 24.2378 29.3629 23.9923 29.1783C24.7877 28.4635 25.6746 27.8576 26.6297 27.3767C28.7706 26.3129 32.0404 26.334 35.8488 26.5467C41.0948 26.8438 44.7192 29.3738 46.1643 30.5372C46.3273 30.6657 46.4193 30.8631 46.4193 31.0701V35.8445C46.4219 35.9748 46.3864 36.1031 46.3173 36.2136C46.2482 36.3242 46.1484 36.4121 46.03 36.4668C45.9117 36.5215 45.78 36.5406 45.651 36.5216C45.522 36.5026 45.4013 36.4465 45.3038 36.3601C44.2664 35.4516 43.1227 34.6723 41.8978 34.039ZM25.0791 19.7713C24.7341 20.1489 24.3738 20.5073 23.9943 20.8447C24.7283 21.3986 25.5218 21.8643 26.3633 22.2381C28.4333 23.1619 31.2757 23.3421 34.9423 23.3382C38.8812 23.3228 42.7402 22.2248 46.0973 20.1642C46.1962 20.1027 46.2778 20.0168 46.3341 19.9148C46.3905 19.8128 46.4198 19.6981 46.4193 19.5816V13.7223C46.4216 13.5971 46.3889 13.4736 46.3249 13.3659C46.2609 13.2582 46.168 13.1705 46.0569 13.1128C45.9457 13.055 45.8206 13.0294 45.6957 13.0389C45.5707 13.0484 45.4509 13.0926 45.3498 13.1665C44.2918 13.9389 42.7182 14.9241 40.6884 15.6965C39.8312 16.2093 38.9163 16.6191 37.9629 16.9174C36.1557 17.4769 34.2467 17.6281 32.3739 17.3602C29.8171 16.7142 27.0073 17.6572 25.0791 19.7713Z"
          fill="#E11312"
        />
        <path
          d="M3.87116 31.5358C3.68333 30.9934 3.507 30.405 3.35366 29.7783L2.20175 30.2766L2.173 30.3207L2.16533 30.3648V36.2451C2.16533 36.3352 2.242 36.3773 2.2995 36.3352L5.232 34.4051C4.75604 33.65 4.35789 32.8486 4.04366 32.0131L3.87116 31.5358ZM43.8337 22.4643L43.7378 22.0905L43.067 22.3397C41.4871 22.976 39.8386 23.4263 38.1546 23.6813L38.1661 23.7465C38.2006 23.9995 38.214 24.2372 38.2044 24.5477C38.1935 25.0208 38.1422 25.4921 38.0511 25.9564L37.9936 26.2037C39.9961 26.5564 41.9278 27.2329 43.7129 28.2066L43.826 27.5473C43.9372 26.8151 44.0023 26.0676 44.0234 25.3086L44.0272 24.5419C44.0188 24.0285 43.9888 23.5157 43.9372 23.0048L43.9084 22.7978L43.8356 22.4643H43.8337ZM2.16725 14.2015V18.9893C2.16725 19.0143 2.173 19.0353 2.196 19.0641L3.39966 20.2371L3.507 19.8097C3.60091 19.4666 3.71208 19.1216 3.84241 18.7747L4.05325 18.2533C4.31008 17.6573 4.60333 17.1091 4.92533 16.605L5.20325 16.1929L5.14958 16.1623C4.33114 15.6908 3.55233 15.1536 2.82083 14.5561L2.30333 14.1153C2.24966 14.0693 2.16533 14.1095 2.16533 14.2015H2.16725ZM14.0736 31.4496C14.5048 31.6815 14.9802 31.877 15.5111 32.0284L15.5322 32.0342L15.5015 32.1358L15.5379 32.0361L15.9653 32.1473L17.4872 32.521C19.0301 32.9158 21.0617 33.5292 23.4384 34.7252L24.0958 35.0644L24.1437 35.1047C24.4542 35.3692 24.7877 35.6356 25.1462 35.8943L25.7346 36.3218L25.6982 36.3543C27.9023 37.857 30.3154 38.6543 33.0352 38.5163L33.3572 38.4972C36.2686 38.271 38.95 36.8814 40.8897 34.6638L41.1867 34.3054L40.7382 34.0083C39.9015 33.4832 39.0027 33.0641 38.0626 32.7606L37.455 32.5804C35.9057 32.1578 34.287 32.0521 32.6959 32.2699C32.3115 32.3975 31.919 32.4993 31.521 32.5747C28.9948 33.0538 26.6948 32.176 24.7743 30.6063L23.9597 29.9163C23.1972 29.3093 22.3613 28.8006 21.4719 28.4021C19.8025 27.6508 17.5772 27.3709 13.7784 27.3479H12.7779C12.4367 27.3537 12.1358 27.3671 11.8368 27.3863L11.4784 27.4112L11.0567 27.4514L11.0759 27.6431C11.1296 28.1069 11.2561 28.5823 11.4822 29.0614L11.6701 29.4218L11.879 29.7533C12.2987 30.313 12.7645 30.6638 13.0712 30.8612L13.489 31.1257L13.6615 31.2253L14.0736 31.4496ZM25.3302 28.8525L24.9162 29.1688L25.4873 29.6441C26.714 30.6139 28.7725 31.7658 31.2143 31.3078L31.5651 31.233L31.9062 31.1468L32.2896 31.0279L32.5905 30.9877C34.1215 30.7963 35.6742 30.8729 37.179 31.2138L37.6601 31.3327C39.222 31.7509 40.6954 32.4483 42.009 33.3912L42.2083 33.5388L42.6607 33.7803C43.6316 34.3234 44.5493 34.9568 45.4015 35.672L45.6871 35.9154L45.7139 35.9346L45.735 35.9403L45.7868 35.9308C45.8022 35.9235 45.8154 35.9123 45.8251 35.8982C45.8357 35.8824 45.8411 35.8636 45.8404 35.8445V31.0701L45.8308 31.0279L45.8059 30.9973L45.6277 30.8554C45.0719 30.4219 44.4908 30.0219 43.8873 29.6575L43.8452 29.6383C41.7597 28.5397 39.5288 27.7427 37.2192 27.2713C36.7543 27.2066 36.2869 27.1612 35.8182 27.1352L35.0132 27.093C33.4242 27.0163 31.9599 26.9857 30.6527 27.07C29.1232 27.1678 27.8582 27.4208 26.8884 27.9038C26.3435 28.1761 25.8223 28.4935 25.3302 28.8525ZM4.565 20.5342C4.49983 20.7833 4.4385 21.0383 4.381 21.3008L4.197 22.6348L4.14716 22.585C3.87888 24.4315 3.89828 26.3085 4.20466 28.1491L4.26791 28.1203L4.39825 28.8429C4.74819 30.7677 5.48146 32.6025 6.5545 34.2383L6.80558 34.6255C7.66873 35.7649 8.74388 36.7268 9.97191 37.4583C11.5222 38.3832 13.2608 38.9472 15.0587 39.1086C19.5629 39.5111 22.7944 37.2743 23.8639 36.4214L23.5534 36.1608C21.9587 35.3078 20.4292 34.6849 18.8537 34.2058C18.2596 34.0218 17.6846 33.8684 17.0176 33.7074L15.5513 33.3528C14.5882 33.0959 13.6703 32.6925 12.8297 32.1568L12.3562 31.8483C11.8886 31.5224 11.4554 31.1199 11.0778 30.6293C10.8946 30.4011 10.7287 30.1595 10.5814 29.9067L10.4089 29.5847C10.0091 28.7791 9.79932 27.8926 9.79558 26.9933C9.77608 26.6485 9.75947 26.3035 9.74575 25.9583V25.4638L9.76491 25.0613C9.86065 23.7181 10.2867 22.4194 11.0053 21.2805C11.7239 20.1416 12.7127 19.1979 13.8838 18.5332L14.1292 18.399C14.5374 18.1728 14.9533 17.9888 15.3731 17.8374L15.9596 17.5978L16.5461 17.3372L16.7952 17.2203C18.5924 16.3272 20.2893 15.2452 21.8572 13.9926L22.2386 13.6802L22.0852 13.5824C20.6439 12.6758 18.4167 11.6734 15.6069 11.6792L15.0396 11.6945L14.7195 11.7137C11.6337 11.9513 8.86408 13.4808 6.94358 15.8077L6.3935 16.5264C5.64587 17.5898 5.07681 18.7681 4.70875 20.0148L4.56308 20.5342H4.565ZM10.9092 25.4945V25.9258L10.9283 26.3033L11.0012 26.2938C11.2503 26.2669 11.5033 26.2458 11.7621 26.2305L12.4099 26.1998C12.6342 26.1922 12.8699 26.1883 13.1267 26.1883H13.8014C17.7497 26.2152 20.0938 26.5123 21.953 27.3479C22.643 27.6584 23.3062 28.0283 23.9348 28.4596C24.6817 27.8284 25.5007 27.288 26.3747 26.8496L26.8271 26.6445C27.9081 26.2037 29.1846 25.9852 30.578 25.897C32.1765 25.7916 33.9743 25.8529 35.8814 25.9603C36.2072 25.9794 36.5293 26.0063 36.8417 26.0388L36.9452 25.5328C37.0027 25.1993 37.0352 24.862 37.0429 24.5189C37.0467 24.3522 37.0448 24.2199 37.0353 24.0934L37.0008 23.8251C36.3414 23.8903 35.6552 23.9248 34.9442 23.9267L34.2235 23.9248C30.9 23.9056 28.1802 23.6928 26.1294 22.7767L25.6081 22.5313C24.92 22.1866 24.2644 21.7806 23.6492 21.3181L24.0307 21.5941C23.3236 22.171 22.5548 22.6679 21.7383 23.0757C19.745 24.0647 17.1 24.2333 12.2317 23.9593C11.8673 23.9385 11.5036 23.9072 11.1411 23.8653C11.0012 24.3924 10.9226 24.9387 10.9092 25.4945ZM37.6428 17.6208C36.0761 18.0412 34.4458 18.1725 32.832 18.008L32.234 17.9313C30.4572 17.4828 28.5252 17.8719 26.9095 18.9568C26.5645 19.1983 26.2387 19.4704 25.9282 19.7713L25.6253 20.0838C25.4029 20.3274 25.1728 20.5639 24.9353 20.7929L25.2056 20.9654C25.6522 21.2414 26.116 21.4868 26.5971 21.7014L26.9957 21.8643C28.9047 22.5927 31.5057 22.7537 34.9403 22.7498L35.5882 22.7383C36.1727 22.7192 36.7382 22.677 37.2844 22.6118L37.3726 22.5965C38.3692 22.4259 39.6342 22.1461 40.8168 21.8375C41.3037 21.711 41.7196 21.5845 42.2217 21.4101C43.4652 20.9415 44.6614 20.3562 45.7944 19.6621L45.827 19.6276L45.8385 19.5816V13.7223L45.8232 13.6706C45.8164 13.6596 45.8075 13.6502 45.7969 13.643C45.7863 13.6357 45.7743 13.6307 45.7616 13.6284C45.749 13.626 45.736 13.6263 45.7235 13.6293C45.711 13.6323 45.6992 13.6379 45.689 13.6457C44.7096 14.3587 43.3008 15.2518 41.5068 16.0032L41.342 16.0703C40.9505 16.3003 40.5467 16.5089 40.1326 16.6951L39.6879 16.8868C39.1822 17.1159 38.6631 17.3143 38.1335 17.4809L37.6428 17.6208ZM15.6702 18.9951L15.4747 19.0679C15.3289 19.1252 15.1851 19.1872 15.0434 19.2538L14.7962 19.3765C14.673 19.4369 14.5515 19.5008 14.432 19.5682C13.8762 19.9074 13.3452 20.3406 12.8488 20.8907C12.4591 21.3226 12.1231 21.8001 11.8483 22.3128L11.6337 22.7518L11.8215 22.7709L12.2968 22.8035L13.5158 22.8648C17.4067 23.0316 19.63 22.8265 21.2151 22.0407C21.9324 21.6824 22.609 21.2481 23.2333 20.745L23.9693 20.0723L24.3182 19.7273L24.9545 19.0564C25.5858 18.4252 26.3097 17.8939 27.1012 17.4809C28.8166 16.5609 30.7658 16.2658 32.5694 16.7219C34.3547 16.9827 36.1757 16.8386 37.8977 16.3003C38.2044 16.2044 38.4977 16.1048 38.7794 15.9993C39.2394 15.8096 39.6572 15.6045 40.0329 15.3975L40.4392 15.1637L40.8858 14.9816L40.5351 14.5599C39.947 13.8866 39.2774 13.2891 38.5417 12.7813L38.1201 12.5148C36.5715 11.594 34.8356 11.0333 33.0409 10.8742C29.4606 10.5541 26.4936 11.7635 24.2511 13.5422L23.9866 13.7568C22.7388 14.9452 20.8317 16.3597 18.7157 17.5327C17.7727 18.0655 16.7473 18.5734 15.6721 18.9932L15.6702 18.9951ZM46.3982 20.6664L46.1624 20.8083L45.5395 21.1667C45.3133 21.2932 45.068 21.4235 44.8054 21.5558L44.999 22.3224C45.045 22.5179 45.0757 22.6981 45.0948 22.8898C45.2944 24.8749 45.187 26.8789 44.7767 28.8314C45.5242 29.301 46.1126 29.7438 46.5266 30.0773C46.6747 30.1968 46.7942 30.3481 46.8761 30.5199C46.958 30.6917 47.0004 30.8797 47 31.0701V35.8445L46.9866 36.0323C46.9579 36.2407 46.8773 36.4384 46.7522 36.6074C46.627 36.7764 46.4614 36.9113 46.2705 36.9994C46.0796 37.0876 45.8696 37.1264 45.6598 37.1121C45.45 37.0979 45.2471 37.0311 45.0699 36.9178L44.9242 36.8028C44.0883 36.0732 43.1817 35.4285 42.2179 34.8785L41.8346 35.3423C39.6937 37.8321 36.7017 39.398 33.4396 39.6491L33.1022 39.6721C29.9704 39.8293 27.2296 38.8728 24.8165 37.1555L24.598 37.3338C23.4384 38.2614 19.9041 40.7224 14.9572 40.2797L14.3515 40.2126C12.5961 39.9715 10.9073 39.3796 9.38541 38.4723L8.9005 38.1694C7.74826 37.4079 6.73231 36.4581 5.89516 35.3596L2.99141 37.2667C2.80406 37.404 2.58218 37.4865 2.35062 37.5049C2.11905 37.5233 1.88694 37.4768 1.68025 37.3708C1.47357 37.2648 1.30047 37.1033 1.18033 36.9045C1.06019 36.7057 0.997749 36.4774 0.999998 36.2451V30.3667C0.999998 30.0447 1.12266 29.7342 1.38333 29.4601L1.53666 29.3298L1.67083 29.2531L3.10833 28.6283C2.92029 27.5913 2.81773 26.5406 2.80166 25.4868V24.7547C2.82658 23.6258 2.94925 22.5716 3.13133 21.6018L1.46766 19.9745C1.32117 19.8556 1.20314 19.7054 1.12222 19.5349C1.0413 19.3644 0.999538 19.178 0.999998 18.9893V14.2015C0.999998 13.1224 2.265 12.534 3.07766 13.2528C3.90375 13.9811 4.84291 14.6634 5.89133 15.2518C8.00925 12.5819 11.1296 10.809 14.6371 10.5426L14.6677 10.9623L14.639 10.5407L14.9763 10.5196C18.7196 10.3318 21.5773 11.7942 23.1969 12.9097L23.2103 12.8963L23.5151 12.6471C25.978 10.6921 29.2152 9.37343 33.1406 9.72226C35.1093 9.8968 37.0135 10.5119 38.7123 11.522C39.9045 12.2254 40.9932 13.2336 41.9937 14.4928C43.0519 13.9851 44.0616 13.382 45.0105 12.6912L45.1696 12.5915C45.3621 12.4923 45.577 12.4446 45.7933 12.453C46.0097 12.4614 46.2203 12.5256 46.4045 12.6394C46.5887 12.7532 46.7404 12.9128 46.8447 13.1025C46.9491 13.2923 47.0026 13.5058 47 13.7223V19.5816L46.9866 19.7694C46.9598 19.9528 46.8933 20.1281 46.7917 20.2831C46.69 20.438 46.5557 20.5689 46.3982 20.6664Z"
          fill="black"
        />
      </svg>
    );
  }
};

export const LanggraphLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M12.198 12H35.8C42.528 12 48 17.6083 48 24.5C48 31.3917 42.528 37 35.802 37H12.2C5.472 37 0 31.3917 0 24.5C0 17.6083 5.472 12 12.198 12ZM23.036 31.375C23.332 31.6958 23.77 31.6792 24.158 31.5958L24.162 31.5979C24.342 31.4479 24.086 31.2583 23.842 31.0771C23.694 30.9687 23.552 30.8646 23.51 30.7708C23.646 30.6042 23.244 30.2187 22.932 29.9208C22.8253 29.8254 22.725 29.7224 22.632 29.6125C22.412 29.3646 22.322 29.0542 22.232 28.7417C22.172 28.5333 22.112 28.325 22.012 28.1333C21.404 26.6875 20.706 25.2521 19.726 24.0292C19.096 23.2167 18.378 22.4875 17.66 21.7583C17.1933 21.2925 16.7378 20.8146 16.294 20.325C15.842 19.8479 15.57 19.2604 15.296 18.6708C15.068 18.1792 14.84 17.6854 14.504 17.2542C13.49 15.7229 10.29 15.3042 9.82 17.4708C9.82 17.5375 9.8 17.5792 9.742 17.6229C9.482 17.8188 9.252 18.0396 9.058 18.3042C8.582 18.9833 8.51 20.1312 9.102 20.7417L9.104 20.7021C9.124 20.3958 9.144 20.1063 9.382 19.8875C9.838 20.2896 10.534 20.4333 11.064 20.1312C11.704 21.0687 11.908 22.2042 12.114 23.3396C12.284 24.2896 12.454 25.2396 12.878 26.0812L12.906 26.1271C13.154 26.55 13.406 26.9812 13.726 27.35C13.844 27.5354 14.082 27.7333 14.32 27.9313C14.634 28.1917 14.948 28.4521 14.978 28.6792V28.9771C14.976 29.5708 14.974 30.1854 15.346 30.6708C15.552 31.0979 15.046 31.525 14.642 31.4729C14.418 31.5042 14.176 31.4437 13.934 31.3854C13.604 31.3021 13.276 31.2229 13.01 31.3792C12.934 31.4625 12.828 31.4625 12.72 31.4667C12.592 31.4708 12.462 31.475 12.386 31.6125C12.37 31.6521 12.334 31.6958 12.296 31.7437C12.212 31.8479 12.122 31.9625 12.23 32.0479L12.26 32.0271C12.424 31.8979 12.58 31.7771 12.8 31.8521C12.772 32.0187 12.878 32.0646 12.984 32.1083L13.038 32.1333C13.0358 32.1726 13.0304 32.2116 13.022 32.25C13.004 32.3437 12.988 32.4333 13.058 32.5146C13.0908 32.479 13.1215 32.4414 13.15 32.4021C13.224 32.3062 13.296 32.2104 13.428 32.1729C13.716 32.5687 14.006 32.4042 14.37 32.2C14.782 31.9687 15.288 31.6833 15.99 32.0854C15.72 32.0729 15.48 32.1062 15.3 32.3354C15.254 32.3854 15.216 32.4437 15.296 32.5104C15.71 32.2354 15.884 32.3333 16.046 32.4271C16.166 32.4937 16.276 32.5583 16.47 32.4771L16.61 32.4021C16.92 32.2292 17.238 32.0562 17.608 32.1167C17.33 32.1979 17.232 32.3771 17.124 32.5708C17.072 32.6687 17.016 32.7687 16.936 32.8625C16.894 32.9062 16.876 32.9583 16.922 33.0292C17.502 32.9812 17.722 32.8313 18.018 32.6292C18.158 32.5333 18.318 32.4229 18.54 32.3083C18.788 32.1521 19.036 32.2521 19.276 32.35C19.536 32.4542 19.786 32.5542 20.018 32.3208C20.092 32.2521 20.184 32.25 20.276 32.25C20.308 32.25 20.342 32.25 20.376 32.2458C20.302 31.85 19.896 31.8542 19.48 31.8583C19 31.8646 18.514 31.8708 18.53 31.2562C18.974 30.9458 18.978 30.4083 18.982 29.9C18.982 29.775 18.982 29.6562 18.992 29.5396C19.318 29.7271 19.664 29.8729 20.008 30.0167C20.332 30.1521 20.654 30.2875 20.956 30.4542C21.272 30.975 21.764 31.6625 22.42 31.6167C22.436 31.5625 22.452 31.5188 22.472 31.4646C22.51 31.4729 22.55 31.4812 22.59 31.4937C22.762 31.5354 22.946 31.5833 23.036 31.375ZM35.894 25.4812C36.274 25.8687 36.788 26.0854 37.326 26.0854C37.864 26.0854 38.378 25.8687 38.758 25.4812C38.9453 25.2917 39.0943 25.065 39.1963 24.8145C39.2983 24.5641 39.3513 24.295 39.352 24.0229C39.3513 23.7509 39.2983 23.4817 39.1963 23.2313C39.0943 22.9809 38.9453 22.7542 38.758 22.5646C38.4783 22.2802 38.1262 22.0851 37.7435 22.0024C37.3607 21.9197 36.9633 21.9529 36.598 22.0979L35.438 20.3667L34.628 20.9333L35.794 22.6729C35.4614 23.0649 35.2862 23.5751 35.3048 24.0979C35.3234 24.6207 35.5344 25.116 35.894 25.4812ZM32.26 19.8771C32.5962 20.0466 32.9703 20.1178 33.3423 20.0829C33.7143 20.0481 34.0703 19.9085 34.372 19.6792C34.7168 19.4176 34.9714 19.0472 35.098 18.6229C35.2233 18.2003 35.2121 17.7466 35.066 17.3312C34.9176 16.9139 34.6441 16.5573 34.286 16.3146C33.8878 16.0425 33.4113 15.9229 32.9378 15.976C32.4644 16.0292 32.0233 16.252 31.69 16.6063C31.4736 16.8393 31.3131 17.1223 31.2217 17.4322C31.1303 17.7421 31.1105 18.0702 31.164 18.3896C31.2203 18.708 31.3485 19.0079 31.538 19.2646C31.728 19.525 31.976 19.7333 32.26 19.8771ZM32.26 31.8604C32.5963 32.0295 32.9703 32.1004 33.3423 32.0656C33.7142 32.0307 34.0701 31.8914 34.372 31.6625C34.7168 31.401 34.9714 31.0305 35.098 30.6062C35.2233 30.1836 35.2121 29.7299 35.066 29.3146C34.9176 28.8972 34.6441 28.5407 34.286 28.2979C33.8879 28.0255 33.4113 27.9056 32.9378 27.9588C32.4642 28.012 32.0231 28.235 31.69 28.5896C31.4734 28.8225 31.3129 29.1055 31.2214 29.4155C31.13 29.7254 31.1103 30.0535 31.164 30.3729C31.2203 30.6913 31.3485 30.9913 31.538 31.2479C31.728 31.5083 31.976 31.7167 32.26 31.8604ZM34.12 24.5354V23.5104H31.02C30.9415 23.198 30.7927 22.9095 30.586 22.6688L31.754 20.9042L30.904 20.3292L29.738 22.0938C29.3806 21.964 28.9956 21.9402 28.6259 22.0248C28.2562 22.1093 27.9163 22.2991 27.644 22.5729C27.4574 22.7612 27.3089 22.9865 27.2072 23.2355C27.1055 23.4846 27.0528 23.7523 27.052 24.0229C27.052 24.5667 27.266 25.0896 27.644 25.4729C27.9163 25.7467 28.2562 25.9365 28.6259 26.0211C28.9956 26.1057 29.3806 26.0818 29.738 25.9521L30.904 27.7167L31.744 27.1417L30.586 25.3771C30.7927 25.1364 30.9415 24.8479 31.02 24.5354H34.12Z"
        fill={props.color || "#1C3C3C"}
      />
    </svg>
  );
};

export const LiteLLMLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M46.1351 11.867C46.1351 23.2641 46.1351 34.6611 46.1351 46.4035C31.5235 46.4035 16.912 46.4035 1.85759 46.4035C1.85759 45.5755 1.85759 44.7475 1.85759 43.8944C2.15287 43.9043 2.44815 43.9142 2.75237 43.9244C3.04205 43.9265 3.33173 43.9285 3.62142 43.9287C3.91857 43.9313 4.21559 43.9431 4.51252 43.955C5.62596 43.9593 5.62596 43.9593 6.52306 43.3699C6.82792 42.9164 7.07895 42.4665 7.31849 41.9758C7.47463 41.6925 7.63134 41.4095 7.78894 41.1271C7.85838 40.996 7.92782 40.8649 7.99937 40.7299C8.28972 40.1942 8.61306 39.6843 8.942 39.1715C8.70627 39.107 8.70627 39.107 8.46578 39.0412C6.39479 38.4621 4.38372 37.8258 2.74315 36.3673C2.62509 36.2685 2.62509 36.2685 2.50464 36.1678C1.94659 35.6313 1.79264 35.2004 1.73479 34.429C1.72947 34.085 1.73069 33.7409 1.73768 33.397C1.73997 33.218 1.74227 33.0391 1.74463 32.8547C1.79323 31.4434 2.02154 30.562 2.89074 29.4305C3.00987 29.2567 3.12782 29.0822 3.24473 28.907C3.56097 28.4356 3.8887 27.9733 4.21906 27.5118C4.31647 27.3656 4.41388 27.2195 4.51425 27.069C4.61166 27.069 4.70907 27.069 4.80943 27.069C4.84767 26.9849 4.88591 26.9008 4.92531 26.8142C5.12129 26.4474 5.34812 26.1543 5.61196 25.8329C5.96422 25.401 6.31379 24.9682 6.65144 24.5248C7.9993 22.761 9.50033 20.8605 11.3035 19.5418C11.4009 19.5418 11.4983 19.5418 11.5986 19.5418C11.5986 19.4444 11.5986 19.347 11.5986 19.2466C11.8918 19.0077 12.1838 18.7891 12.4934 18.5732C12.6327 18.4754 12.6327 18.4754 12.7748 18.3756C16.2055 15.9962 19.9766 14.4493 24.1808 14.3207C25.319 14.2829 26.3495 14.0818 27.4498 13.798C31.6077 12.7434 35.9617 12.3341 40.2314 12.0146C40.3479 12.0059 40.4644 11.9971 40.5844 11.9881C42.4402 11.8541 44.2685 11.867 46.1351 11.867Z"
        fill={props.color || "#3B87C1"}
        opacity={props.color ? 0.4 : 1}
      />
      <path
        d="M46.1351 11.8672C46.1351 15.2278 46.1351 18.5885 46.1351 22.051C44.8688 22.1971 43.6024 22.3432 42.2977 22.4938C41.5868 22.5964 40.8826 22.7013 40.1761 22.8258C40.002 22.8562 39.8278 22.8865 39.6484 22.9177C36.9187 23.4113 34.1232 24.0331 31.5291 25.035C31.1989 25.1617 30.8666 25.281 30.5336 25.4001C28.2079 26.2455 26.0382 27.2752 23.9969 28.6834C19.5189 31.758 14.7727 33.3814 9.38477 34.0059C9.2713 34.0204 9.15782 34.0348 9.04091 34.0497C7.60213 34.2101 6.15336 34.1695 4.70796 34.1627C4.43094 34.162 4.15391 34.1613 3.87689 34.1607C3.20379 34.1591 2.53069 34.1566 1.85759 34.1535C1.84582 33.7072 1.83726 33.2612 1.8305 32.8148C1.82686 32.6896 1.82323 32.5643 1.81949 32.4353C1.80544 31.1964 2.18004 30.4488 2.89074 29.4306C3.00894 29.2547 3.12693 29.0786 3.24472 28.9025C3.56185 28.4334 3.8895 27.9723 4.21906 27.5119C4.31647 27.3658 4.41388 27.2197 4.51424 27.0691C4.61963 26.9635 4.62123 26.9504 4.7089 26.8455C4.7864 26.7528 4.86631 26.658 4.97409 26.5562C5.21057 26.2933 5.34812 26.1545 5.61196 25.833C5.96422 25.4011 6.31379 24.9683 6.65144 24.5249C7.99929 22.7611 9.50032 20.8606 11.3035 19.5419C11.4009 19.5419 11.4983 19.5419 11.5986 19.5419C11.5986 19.4445 11.5986 19.3471 11.5986 19.2467C11.8918 19.0078 12.1838 18.7892 12.4934 18.5734C12.6327 18.4755 12.6327 18.4755 12.7748 18.3758C16.2055 15.9963 19.9766 14.4494 24.1808 14.3209C25.319 14.283 26.3495 14.0819 27.4498 13.7981C31.6077 12.7436 35.9617 12.3342 40.2314 12.0147C40.3479 12.006 40.4644 11.9972 40.5844 11.9882C42.4402 11.8543 44.2685 11.8672 46.1351 11.8672Z"
        fill={props.color || "#BBBDBF"}
        opacity={props.color ? 0.1 : 1}
      />
      <path
        d="M46.1351 22.051C46.1351 26.8241 46.1351 31.5972 46.1351 36.515C32.0593 36.515 17.9835 36.515 3.48112 36.515C3.40806 36.6611 3.40806 36.6611 3.33353 36.8102C3.14232 36.6479 2.95181 36.4848 2.76161 36.3213C2.65544 36.2305 2.54928 36.1398 2.4399 36.0463C2.11113 35.738 1.87293 35.4946 1.82878 35.0327C1.83839 34.7397 1.848 34.4466 1.85761 34.1535C2.10152 34.1491 2.10152 34.1491 2.35036 34.1447C7.29501 34.0526 12.2071 33.9597 16.912 32.2348C17.0266 32.1932 17.1412 32.1516 17.2593 32.1086C19.6731 31.219 21.7662 30.0482 23.8918 28.6116C26.4998 26.8583 29.2706 25.6523 32.2615 24.7077C32.4769 24.6343 32.6922 24.5606 32.9072 24.4863C33.1345 24.4116 33.3621 24.338 33.5898 24.2649C33.7576 24.2105 33.9254 24.1561 34.0983 24.1C35.9642 23.5136 37.8678 23.1461 39.7887 22.789C39.913 22.7657 40.0373 22.7425 40.1654 22.7186C40.875 22.5869 41.5856 22.4637 42.2977 22.3462C42.4792 22.3157 42.6608 22.2853 42.8478 22.2539C43.1097 22.2128 43.1097 22.2128 43.377 22.1709C43.5256 22.1475 43.6742 22.1241 43.8273 22.1C44.5935 22.0301 45.3657 22.051 46.1351 22.051Z"
        fill={props.color || "#3B88C3"}
        opacity={props.color ? 0.4 : 1}
      />
      <path
        d="M46.1351 19.5419C46.1351 20.3699 46.1351 21.1979 46.1351 22.051C45.7941 22.0861 45.7941 22.0861 45.4462 22.1219C40.7695 22.6212 35.9406 23.3312 31.5291 25.035C31.1989 25.1617 30.8666 25.281 30.5336 25.4001C28.2078 26.2455 26.0382 27.2752 23.9969 28.6834C19.5189 31.758 14.7727 33.3814 9.38477 34.0059C9.27129 34.0204 9.15782 34.0348 9.04091 34.0497C7.60212 34.2101 6.15336 34.1695 4.70796 34.1627C4.43093 34.162 4.15391 34.1613 3.87689 34.1607C3.20378 34.1592 2.38553 34.1656 1.71242 34.1627C1.71242 33.3348 1.71242 32.5291 1.85759 31.6445C2.03439 31.6415 2.21118 31.6386 2.39333 31.6356C3.06456 31.6242 3.73575 31.6111 4.40694 31.5976C4.69457 31.592 4.98221 31.5869 5.26986 31.5822C7.83102 31.5399 10.3951 31.4435 12.8993 30.8604C13.0424 30.8277 13.1855 30.795 13.3329 30.7612C16.6412 29.9855 19.6551 28.571 22.4496 26.6396C24.7038 25.082 27.1238 23.8515 29.715 22.9585C30.1168 22.8166 30.5049 22.6616 30.8963 22.4938C32.7014 21.7611 34.6586 21.3661 36.5535 20.9353C36.8135 20.8756 37.0728 20.8134 37.3321 20.7509C39.2128 20.3 41.111 19.9947 43.0265 19.7356C43.1662 19.7162 43.3059 19.6968 43.4499 19.6768C44.3526 19.559 45.2247 19.5246 46.1351 19.5419Z"
        fill={props.color || "#E4E6E7"}
        opacity={props.color ? 0.2 : 1}
      />
      <path
        d="M3.48109 36.515C17.5569 36.515 31.6327 36.515 46.1351 36.515C46.1351 37.343 46.1351 38.171 46.1351 39.024C41.3745 39.0477 36.6139 39.0659 31.8532 39.0768C31.2896 39.0781 30.7261 39.0794 30.1625 39.0808C30.0503 39.0811 29.9382 39.0813 29.8226 39.0816C28.0122 39.0861 26.2019 39.0942 24.3916 39.1038C22.5305 39.1135 20.6694 39.1192 18.8082 39.1213C17.6622 39.1227 16.5162 39.1271 15.3702 39.1355C14.4876 39.1417 13.605 39.1423 12.7224 39.1409C12.3631 39.1412 12.0039 39.1432 11.6446 39.1472C8.8545 39.1767 5.72334 38.8333 3.48109 36.9578C3.48109 36.8116 3.48109 36.6655 3.48109 36.515Z"
        fill={props.color || "#226799"}
        opacity={props.color ? 0.8 : 1}
      />
      <path
        d="M21.8904 41.515C22.1628 41.5152 22.1628 41.5152 22.4408 41.5154C22.5416 41.5153 22.6423 41.5151 22.7462 41.515C23.0863 41.5148 23.4265 41.5156 23.7667 41.5164C24.0095 41.5164 24.2524 41.5164 24.4952 41.5163C25.1566 41.5163 25.818 41.5171 26.4794 41.5182C27.1698 41.5191 27.8602 41.5192 28.5506 41.5193C29.859 41.5198 31.1673 41.521 32.4756 41.5225C33.9647 41.5241 35.4538 41.525 36.9429 41.5257C40.007 41.5272 43.0711 41.5299 46.1351 41.5331C46.1351 42.7508 46.1351 43.9684 46.1351 45.2229C37.2221 45.2229 28.309 45.2229 19.1258 45.2229C19.3502 44.6621 19.5314 44.2864 19.8644 43.8047C19.9493 43.6812 20.0343 43.5577 20.1218 43.4305C20.2102 43.3036 20.2985 43.1767 20.3896 43.0459C20.4785 42.9168 20.5675 42.7876 20.6591 42.6545C21.4319 41.5356 21.4319 41.5356 21.8904 41.515Z"
        fill={props.color || "#232021"}
        opacity={props.color ? 1 : 1}
      />
      <path
        d="M25.1771 14.3762C24.9828 15.1364 24.6312 15.6192 24.0978 16.175C24.0187 16.2584 23.9395 16.3419 23.8579 16.4278C21.5553 18.8135 18.7258 20.5365 15.8788 22.1986C15.7333 22.2872 15.5877 22.3759 15.4378 22.4673C14.2025 23.2128 12.9137 23.8231 11.5986 24.4125C11.4006 24.5015 11.4006 24.5015 11.1985 24.5923C9.44356 25.3351 7.5851 25.8157 5.69498 26.036C5.9275 25.2629 6.42084 24.7236 6.93106 24.1173C7.02328 24.0067 7.11551 23.8962 7.21053 23.7823C9.79928 20.7015 9.79928 20.7015 11.3035 19.5419C11.3973 19.4605 11.3695 19.4786 11.4852 19.3768C11.5898 19.2555 11.5508 19.3029 11.5986 19.2468C11.8918 19.0078 12.1838 18.7892 12.4934 18.5734C12.5863 18.5082 12.6791 18.4429 12.7748 18.3758C16.5058 15.7881 20.6009 14.2099 25.1771 14.3762Z"
        fill={props.color || "#595A5C"}
        opacity={props.color ? 0.6 : 1}
      />
      <path
        d="M46.1351 11.8672C46.1351 12.7439 46.1351 13.6206 46.1351 14.5238C45.7616 14.5295 45.7616 14.5295 45.3805 14.5353C42.4568 14.5984 39.5872 14.8813 36.6893 15.2618C36.5451 15.2804 36.4009 15.2991 36.2524 15.3183C31.1259 15.9984 25.7915 17.3234 21.0865 19.4964C20.4887 19.7708 19.9074 19.9659 19.2735 20.1323C19.6808 19.7043 20.0982 19.3616 20.5833 19.0254C23.1498 17.2543 23.1498 17.2543 24.9131 14.8046C25.2183 14.3094 25.4487 14.2159 26.0029 14.0735C26.117 14.0486 26.2311 14.0237 26.3486 13.998C26.5306 13.9544 26.5306 13.9544 26.7162 13.9099C28.4997 13.4972 30.3082 13.1964 32.1139 12.9003C32.2482 12.8779 32.3824 12.8554 32.5207 12.8323C37.004 12.0926 41.5971 11.829 46.1351 11.8672Z"
        fill={props.color || "#A6A8AC"}
        opacity={props.color ? 0.2 : 1}
      />
      <path
        d="M6.13777 43.747C6.18647 43.8444 6.23518 43.9418 6.28536 44.0422C6.04183 44.4318 5.7983 44.8215 5.5474 45.2229C18.9413 45.2229 32.3353 45.2229 46.1351 45.2229C46.1351 45.6126 46.1351 46.0022 46.1351 46.4037C31.5235 46.4037 16.912 46.4037 1.8576 46.4037C1.8576 45.5757 1.8576 44.7477 1.8576 43.8946C3.27006 43.8459 4.68251 43.7972 6.13777 43.747Z"
        fill={props.color || "#939598"}
        opacity={props.color ? 0.5 : 1}
      />
      <path
        d="M9.13574 26.7278C9.3239 26.7284 9.51206 26.7289 9.70592 26.7295C10.419 26.7856 11.0789 26.8445 11.5987 27.3643C11.6079 27.7056 11.6079 27.7056 11.4511 28.1022C10.7282 28.7002 10.1121 28.9903 9.20031 29.1815C9.0913 29.2059 8.9823 29.2303 8.86999 29.2555C8.00451 29.4375 7.16937 29.4993 6.28537 29.4306C6.36769 28.8181 6.56421 28.4371 6.92186 27.9362C7.00957 27.811 7.09727 27.6858 7.18764 27.5568C7.79298 26.8174 8.17311 26.7152 9.13574 26.7278Z"
        fill={props.color || "#FCE7B6"}
        opacity={props.color ? 0.5 : 1}
      />
      <path
        d="M3.48109 36.515C17.5569 36.515 31.6327 36.515 46.1351 36.515C46.1351 36.5637 46.1351 36.6124 46.1351 36.6626C32.2054 36.7113 18.2757 36.76 3.92387 36.8102C4.02128 36.9076 4.11869 37.005 4.21905 37.1054C4.07294 37.1784 4.07294 37.1784 3.92387 37.2529C3.77775 37.1555 3.63164 37.0581 3.48109 36.9578C3.48109 36.8116 3.48109 36.6655 3.48109 36.515Z"
        fill={props.color || "#286EA3"}
        opacity={props.color ? 0.3 : 1}
      />
    </svg>
  );
};

export const StrandsLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M19.0645 6.24463C17.8001 5.88481 16.4714 6.57751 16.0967 7.79186C15.7221 9.00621 16.4433 10.2823 17.7077 10.6421L33.3224 14.8869C34.3679 15.1844 34.4852 16.5613 33.5042 17.0181L12.3668 26.8604C7.45408 29.148 8.0523 36.0454 13.2914 37.5209L28.6087 41.8346L28.6681 41.8505C29.9155 42.1691 31.2067 41.4756 31.572 40.2793C31.9432 39.064 31.218 37.7897 29.9525 37.4333L14.6352 33.1196C13.5874 32.8245 13.4677 31.445 14.4502 30.9875L35.5877 21.1452C40.493 18.8611 39.906 11.9767 34.6793 10.4893L19.0645 6.24463Z"
        fill="#989898"
      />
      <path
        d="M35.8083 1.09755C37.0747 0.744178 38.3997 1.44369 38.7677 2.65992C39.1356 3.87617 38.4072 5.14865 37.1408 5.50203L16.3627 11.2999C13.2118 12.1792 12.848 16.3263 15.8013 17.6994L33.9665 26.1453C40.8488 29.3453 40.0132 39.0067 32.6754 41.0723L12.1453 46.8512C10.8797 47.2075 9.55315 46.511 9.18223 45.2955C8.81132 44.0802 9.53652 42.8061 10.802 42.4499L31.3322 36.6709C34.4769 35.7856 34.8351 31.645 31.8854 30.2736L13.7203 21.8277C6.8293 18.6237 7.67822 8.94711 15.0303 6.89558L35.8083 1.09755Z"
        fill={props.color || "#00FF77"}
      />
    </svg>
  );
};

export const PydanticLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M47.3095 33.3434L25.4759 3.09554C24.7885 2.15134 23.2042 2.15134 22.5238 3.09554L0.690085 33.3434C0.467845 33.6529 0.348303 34.0244 0.348297 34.4054C0.34883 34.7881 0.469554 35.1609 0.69342 35.4712C0.917286 35.7815 1.23298 36.0137 1.59593 36.1349L23.4299 43.2758H23.4334C23.7999 43.3953 24.1949 43.3953 24.5615 43.2758H24.5649L46.3986 36.1353C46.671 36.0471 46.9185 35.8953 47.1206 35.6924C47.3228 35.4896 47.4737 35.2415 47.5609 34.9688C47.6508 34.697 47.6744 34.4077 47.6298 34.125C47.5851 33.8423 47.4736 33.5743 47.3044 33.3434H47.3095ZM24.0008 7.26377L32.752 19.3883L24.5683 16.7126C24.505 16.692 24.4382 16.6955 24.3752 16.6804C24.3133 16.6651 24.2503 16.6548 24.1868 16.6496C24.1238 16.641 24.0639 16.617 24.0008 16.617C23.9375 16.617 23.8793 16.641 23.8159 16.6496C23.7529 16.6564 23.6895 16.6667 23.6282 16.6804C23.5632 16.6924 23.4964 16.6924 23.4385 16.7126L15.3041 19.3729L15.2527 19.3901L24.0043 7.26309H24.0008V7.26377ZM11.6362 24.3963L21.1634 21.2784L22.1806 20.9473V39.0429L5.09805 33.4544L11.6362 24.3963ZM25.8228 39.0394V20.9473L36.3668 24.3963L42.905 33.4492L25.8211 39.0394H25.8228Z"
        fill={props.color || "#E520E9"}
      />
    </svg>
  );
};

export const ClaudeLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M13.76 0.279992C13.819 0.286844 13.8779 0.293683 13.9369 0.30051C14.0983 0.31963 14.2595 0.340405 14.4206 0.361867C14.5209 0.375111 14.6213 0.387706 14.7217 0.400305C14.7862 0.408807 14.8506 0.417347 14.915 0.42593C14.9596 0.431375 14.9596 0.431375 15.0051 0.43693C15.2794 0.474894 15.4334 0.586003 15.6325 0.769992C15.7714 0.898969 15.7714 0.898969 15.9172 1.01988C16.1447 1.20147 16.2507 1.38397 16.3625 1.64999C16.3808 1.69143 16.399 1.73287 16.4178 1.77556C16.4686 1.89171 16.5186 2.00817 16.5681 2.12487C16.6007 2.20149 16.6334 2.27803 16.6661 2.35456C16.6773 2.38083 16.6886 2.40709 16.7002 2.43415C16.7173 2.47432 16.7173 2.47432 16.7349 2.5153C16.8085 2.68753 16.8821 2.8598 16.9554 3.03216C17.2355 3.68904 17.5318 4.33852 17.8275 4.98845C17.9603 5.28016 18.0917 5.57243 18.2225 5.86499C18.2633 5.95603 18.3041 6.04708 18.3449 6.13812C18.4183 6.30198 18.4916 6.46586 18.5649 6.62976C19.0882 7.80152 19.0882 7.80152 19.64 8.95999C19.6567 8.99392 19.6734 9.02785 19.6905 9.0628C19.8843 9.45654 20.0844 9.84659 20.29 10.2342C20.4276 10.4944 20.5593 10.7572 20.69 11.0208C20.8036 11.2489 20.9206 11.4749 21.04 11.7C21.1923 11.9869 21.3397 12.2759 21.483 12.5674C21.5926 12.789 21.707 13.0078 21.8225 13.2264C22.0382 13.6346 22.2487 14.0453 22.4584 14.4566C22.5199 14.5773 22.5815 14.6979 22.6432 14.8185C22.724 14.9767 22.8044 15.135 22.8845 15.2935C22.9579 15.4383 23.0328 15.582 23.1093 15.7251C23.3666 16.2113 23.5317 16.7006 23.6775 17.23C23.7105 17.3469 23.7437 17.4638 23.777 17.5806C23.7974 17.6523 23.8175 17.7241 23.8374 17.7959C23.895 18.002 23.9621 18.2006 24.04 18.4C24.1324 18.4132 24.2248 18.4264 24.32 18.44C24.3185 18.3895 24.3185 18.3895 24.3169 18.3381C24.3067 17.6995 24.3614 17.0637 24.41 16.4275C24.4125 16.3943 24.415 16.3611 24.4176 16.327C24.515 15.0387 24.6546 13.7568 24.803 12.4736C25.0234 10.5661 25.1908 8.65604 25.3469 6.74252C25.3866 6.25656 25.4275 5.77072 25.4694 5.28494C25.4794 5.16694 25.4892 5.04893 25.499 4.9309C25.5599 4.19909 25.6474 3.53784 25.9625 2.86499C25.9918 2.79851 26.0209 2.73194 26.0498 2.6653C26.1135 2.51982 26.1782 2.37498 26.2463 2.23151C26.2777 2.16488 26.3074 2.09744 26.3369 2.02996C26.409 1.9043 26.4764 1.84786 26.5955 1.7664C26.6542 1.7258 26.6542 1.7258 26.7141 1.68439C26.7557 1.65654 26.7972 1.62869 26.84 1.59999C26.8815 1.57157 26.923 1.54316 26.9658 1.51388C27.0938 1.42701 27.223 1.34208 27.3525 1.25749C27.3962 1.22808 27.4399 1.19866 27.485 1.16835C27.5485 1.12701 27.5485 1.12701 27.6133 1.08484C27.6511 1.05985 27.689 1.03487 27.728 1.00912C27.9007 0.933387 28.0179 0.979143 28.1908 1.03717C28.8074 1.29017 29.2068 1.52948 29.5439 2.10974C29.6007 2.20106 29.663 2.2817 29.7314 2.36452C29.7526 2.39117 29.7738 2.41781 29.7957 2.44527C29.8349 2.49428 29.8753 2.54242 29.9172 2.58925C30.0351 2.74519 30.047 2.86883 30.0226 3.06215C30.0186 3.09039 30.0145 3.11863 30.0104 3.14772C30.0065 3.1777 30.0025 3.20767 29.9985 3.23856C29.9861 3.33243 29.9731 3.42621 29.96 3.51999C29.9526 3.57599 29.9452 3.632 29.9378 3.68802C29.8744 4.16603 29.8036 4.64307 29.7325 5.11999C29.7265 5.16056 29.7204 5.20114 29.7142 5.24294C29.3509 7.67276 28.8638 10.0807 28.3906 12.491C28.0907 14.0188 27.8045 15.5492 27.52 17.08C27.5774 17.0757 27.6347 17.0707 27.692 17.0656C27.724 17.0629 27.7559 17.0602 27.7888 17.0575C27.9386 17.0288 28.0263 16.936 28.125 16.8275C28.1576 16.7933 28.1576 16.7933 28.1908 16.7584C28.4596 16.4681 28.6891 16.1459 28.9217 15.8266C29.0528 15.6483 29.1878 15.473 29.3225 15.2975C29.5274 15.0298 29.7288 14.7599 29.9275 14.4875C30.1419 14.1942 30.3659 13.9108 30.5967 13.6305C30.7134 13.4887 30.8271 13.3448 30.94 13.2C31.1193 12.9704 31.3024 12.7441 31.4866 12.5184C31.6262 12.347 31.764 12.1743 31.9 12C32.0793 11.7704 32.2624 11.5441 32.4466 11.3184C32.5862 11.147 32.7239 10.9742 32.86 10.8C33.2136 10.3481 33.5722 9.9031 33.96 9.47999C34.0069 9.42681 34.0535 9.37348 34.1 9.31999C34.2357 9.16419 34.3741 9.01088 34.5125 8.85749C34.5891 8.77222 34.6648 8.68638 34.74 8.59999C35.1521 8.12794 35.5821 7.67275 36.0125 7.21749C36.0409 7.18741 36.0693 7.15732 36.0986 7.12633C36.153 7.06879 36.2074 7.01131 36.2618 6.95389C36.3795 6.82928 36.4943 6.70403 36.6036 6.57202C36.7276 6.42585 36.8697 6.31509 37.0251 6.2031C37.1432 6.11679 37.2488 6.03136 37.3532 5.92807C37.7755 5.51536 38.2725 5.47698 38.8425 5.46249C38.9145 5.46011 38.9145 5.46011 38.9879 5.45767C39.4169 5.45195 39.8389 5.46462 40.1686 5.76757C40.5385 6.17149 40.7869 6.66864 41.0194 7.16025C41.1185 7.36623 41.2369 7.52988 41.3942 7.69658C41.4707 7.86921 41.4078 8.02325 41.36 8.19999C41.3509 8.23533 41.3417 8.27066 41.3323 8.30707C41.3021 8.42055 41.2704 8.53349 41.2382 8.6464C41.2205 8.70819 41.2205 8.70819 41.2026 8.77124C41.1778 8.85775 41.1529 8.94424 41.1279 9.0307C41.0901 9.16211 41.0532 9.29374 41.0163 9.42538C40.992 9.5099 40.9676 9.59441 40.9432 9.6789C40.927 9.73723 40.927 9.73723 40.9105 9.79673C40.7952 10.1883 40.5703 10.4777 40.3125 10.7875C40.2666 10.8433 40.2207 10.8992 40.1749 10.9551C40.1522 10.9828 40.1295 11.0104 40.1061 11.0389C40.0076 11.1598 39.9111 11.2822 39.815 11.405C39.7972 11.4278 39.7794 11.4505 39.761 11.474C39.7252 11.5198 39.6895 11.5655 39.6537 11.6113C39.5627 11.7277 39.4714 11.8438 39.38 11.96C39.3432 12.0069 39.3064 12.0537 39.2696 12.1006C39.1978 12.1919 39.1261 12.2832 39.0542 12.3744C38.6194 12.9269 38.1881 13.4822 37.76 14.04C37.7102 14.1048 37.6603 14.1697 37.6104 14.2345C37.5768 14.2782 37.5432 14.3219 37.5096 14.3656C37.4285 14.4711 37.3473 14.5764 37.2657 14.6816C37.0473 14.9634 36.8349 15.2491 36.6242 15.5367C36.4423 15.7842 36.2568 16.0287 36.07 16.2725C35.8803 16.5202 35.694 16.7692 35.5148 17.0245C35.3614 17.2415 35.1993 17.4517 35.0375 17.6625C34.9314 17.8015 34.8256 17.9406 34.72 18.08C34.7021 18.1036 34.6842 18.1272 34.6658 18.1516C34.3657 18.5528 34.1274 18.9944 33.8801 19.4291C33.7346 19.6851 33.5873 19.94 33.44 20.195C33.4161 20.2365 33.3922 20.278 33.3675 20.3208C33.3444 20.3609 33.3213 20.4009 33.2975 20.4422C33.2752 20.4813 33.2529 20.5203 33.2299 20.5606C33.181 20.6441 33.1306 20.7267 33.0789 20.8085C33.0529 20.8503 33.0269 20.8922 33 20.9353C32.9761 20.9727 32.9522 21.0101 32.9275 21.0486C32.8695 21.1848 32.8911 21.2576 32.92 21.4C33.4935 21.3191 34.057 21.2037 34.6224 21.0794C34.78 21.0447 34.9377 21.0104 35.0953 20.9762C35.1225 20.9703 35.1497 20.9644 35.1777 20.9583C35.2333 20.9462 35.289 20.9341 35.3447 20.922C35.4298 20.9035 35.515 20.885 35.6002 20.8665C37.7839 20.3914 39.969 19.946 42.1727 19.5724C42.5329 19.5112 42.8928 19.4482 43.2528 19.3853C43.5364 19.3358 43.82 19.2867 44.1037 19.2375C44.1709 19.2259 44.2381 19.2143 44.3053 19.2026C44.4926 19.1701 44.68 19.1378 44.8674 19.1058C44.9235 19.0963 44.9796 19.0866 45.0357 19.0768C45.1123 19.0635 45.189 19.0505 45.2657 19.0376C45.3083 19.0303 45.351 19.023 45.3949 19.0154C45.5936 18.9909 45.7775 18.9722 45.9615 19.0611C46.0039 19.0812 46.0039 19.0812 46.0472 19.1016C46.0915 19.1236 46.0915 19.1236 46.1367 19.1459C46.1679 19.1608 46.1991 19.1757 46.2313 19.1911C46.3303 19.2386 46.4289 19.2868 46.5275 19.335C46.5943 19.3671 46.661 19.3992 46.7278 19.4312C46.8761 19.5026 47.0244 19.574 47.172 19.647C47.2261 19.6733 47.2813 19.6974 47.3368 19.7207C47.4384 19.7839 47.4466 19.8825 47.4729 19.9931C47.4794 20.0234 47.4859 20.0538 47.4925 20.085C47.4998 20.1144 47.507 20.1438 47.5145 20.1741C47.5902 20.5087 47.5273 20.7769 47.395 21.0859C47.3828 21.1155 47.3705 21.145 47.3578 21.1755C47.3189 21.2688 47.2795 21.3619 47.24 21.455C47.2135 21.5184 47.1871 21.5817 47.1607 21.6451C46.9736 22.0929 46.9736 22.0929 46.92 22.2C46.8291 22.2312 46.8291 22.2312 46.7065 22.2615C46.6376 22.2788 46.6376 22.2788 46.5673 22.2964C46.5166 22.3087 46.466 22.321 46.4138 22.3337C46.3604 22.347 46.307 22.3603 46.2536 22.3736C46.1112 22.409 45.9687 22.4439 45.8262 22.4788C45.6881 22.5127 45.5502 22.547 45.4122 22.5812C45.1538 22.6453 44.8953 22.7087 44.6367 22.7718C44.4491 22.8178 44.2617 22.8647 44.0744 22.9123C43.1037 23.1584 42.1287 23.3621 41.1451 23.5495C39.5463 23.8542 37.9573 24.1884 36.375 24.57C36.3333 24.5801 36.2916 24.5901 36.2486 24.6005C35.8794 24.6896 35.5104 24.7792 35.1415 24.8693C34.4082 25.0484 33.6736 25.2223 32.9387 25.395C32.7519 25.439 32.5651 25.483 32.3783 25.5271C32.3498 25.5338 32.3212 25.5405 32.2919 25.5475C32.2369 25.5604 32.1819 25.5734 32.1269 25.5864C31.9379 25.631 31.749 25.6755 31.56 25.72C31.56 25.7596 31.56 25.7992 31.56 25.84C31.5967 25.8415 31.6334 25.843 31.6712 25.8445C32.0822 25.8634 32.4915 25.8971 32.9011 25.935C32.929 25.9376 32.957 25.9401 32.9857 25.9428C33.2192 25.9644 33.4526 25.9863 33.686 26.0083C33.8607 26.0248 34.0355 26.0411 34.2102 26.0572C34.2901 26.0647 34.37 26.0723 34.4499 26.0799C35.0695 26.1373 35.6861 26.1658 36.3084 26.1595C38.3533 26.1412 40.38 26.2414 42.4175 26.4C42.4476 26.4023 42.4777 26.4047 42.5087 26.4071C43.1419 26.4563 43.7751 26.5068 44.4081 26.5594C44.4723 26.5647 44.4723 26.5647 44.5378 26.5701C44.6519 26.5797 44.766 26.5897 44.88 26.6C44.914 26.6028 44.948 26.6055 44.9831 26.6084C45.145 26.6232 45.2738 26.6378 45.4139 26.7267C45.4577 26.7539 45.4577 26.7539 45.5023 26.7816C45.5477 26.8114 45.5477 26.8114 45.5939 26.8419C45.6264 26.8626 45.6588 26.8834 45.6922 26.9048C45.7963 26.9722 45.8995 27.0409 46.0025 27.11C46.0376 27.1333 46.0727 27.1566 46.1089 27.1806C46.6338 27.5318 47.056 27.8749 47.3821 28.4259C47.4678 28.565 47.5675 28.6934 47.667 28.8229C47.7692 29.01 47.718 29.1989 47.68 29.4C47.6748 29.4434 47.6695 29.4868 47.6641 29.5316C47.6161 29.9038 47.6161 29.9038 47.44 30.04C47.3087 30.1143 47.1735 30.1797 47.0375 30.245C46.9578 30.284 46.878 30.3232 46.7983 30.3623C46.7583 30.382 46.7182 30.4016 46.677 30.4218C46.5366 30.4916 46.3977 30.564 46.2592 30.6375C46.1965 30.6708 46.1337 30.7037 46.0706 30.7364C45.8978 30.826 45.7312 30.92 45.5684 31.0269C45.2893 31.1787 45.1241 31.2215 44.8201 31.1421C44.7875 31.1344 44.7549 31.1267 44.7212 31.1187C44.6507 31.102 44.5802 31.0848 44.5098 31.0673C44.3995 31.0399 44.289 31.0138 44.1783 30.988C43.9314 30.9304 43.6849 30.8711 43.4385 30.8117C43.3452 30.7893 43.252 30.767 43.1588 30.7446C42.9606 30.6971 42.7624 30.6495 42.5642 30.6018C42.0643 30.4816 41.5643 30.3618 41.0644 30.2419C40.9837 30.2226 40.9031 30.2033 40.8225 30.1839C39.8 29.9388 38.7771 29.6958 37.7539 29.4537C36.6848 29.2007 35.6156 28.9476 34.5475 28.69C34.502 28.679 34.4564 28.668 34.4094 28.6567C33.9405 28.5436 33.4722 28.4285 33.0047 28.3098C32.9635 28.2994 32.9222 28.289 32.8797 28.2782C32.6883 28.2297 32.4971 28.1809 32.306 28.1314C31.9779 28.0472 31.6607 27.9706 31.32 27.96C31.3719 28.2206 31.541 28.3605 31.725 28.5375C31.7544 28.5665 31.7838 28.5955 31.8141 28.6254C31.9479 28.7565 32.0832 28.8839 32.226 29.0051C32.5193 29.2586 32.7854 29.5433 33.0579 29.8186C33.3248 30.0882 33.5902 30.3548 33.88 30.6C33.9607 30.6726 34.0403 30.7463 34.12 30.82C34.2511 30.9408 34.3827 31.0609 34.5175 31.1775C34.6848 31.3224 34.8465 31.4733 35.0088 31.6238C35.1494 31.7539 35.2919 31.8814 35.4367 32.0067C35.5654 32.1199 35.6912 32.2363 35.8174 32.3523C36.0103 32.5297 36.205 32.705 36.4 32.88C36.4667 32.94 36.5334 33 36.6 33.06C36.633 33.0897 36.666 33.1194 36.7 33.15C37 33.42 37 33.42 37.1008 33.5108C37.1648 33.5683 37.2288 33.6257 37.293 33.683C37.5217 33.8875 37.7465 34.0959 37.9705 34.3055C38.1323 34.4568 38.2946 34.6075 38.4575 34.7575C38.6804 34.9627 38.9014 35.1698 39.1223 35.3772C39.3091 35.5526 39.497 35.7269 39.6855 35.9005C39.851 36.0529 40.0156 36.2063 40.18 36.36C40.3661 36.5339 40.5527 36.7074 40.74 36.88C40.9256 37.051 41.1106 37.2227 41.295 37.395C41.3199 37.4182 41.3447 37.4413 41.3703 37.4652C41.5008 37.5872 41.6313 37.7093 41.7616 37.8316C41.7881 37.8564 41.8147 37.8813 41.842 37.9069C41.8922 37.954 41.9424 38.0011 41.9926 38.0483C42.0272 38.0807 42.0272 38.0807 42.0625 38.1137C42.0851 38.1352 42.1076 38.1567 42.1309 38.1788C42.1985 38.2413 42.1985 38.2413 42.2748 38.2905C42.5346 38.4727 42.563 38.724 42.62 39.0225C42.6358 39.0956 42.6358 39.0956 42.6519 39.1702C42.7492 39.6421 42.7492 39.6421 42.629 39.8469C42.608 39.8752 42.5869 39.9036 42.5652 39.9328C42.5307 39.9798 42.5307 39.9798 42.4956 40.0276C42.4715 40.0598 42.4474 40.0919 42.4225 40.125C42.3751 40.1893 42.3276 40.2535 42.2802 40.3178C42.2485 40.3602 42.2485 40.3602 42.2162 40.4034C42.1691 40.4677 42.1242 40.5337 42.08 40.6C41.4749 40.5718 41.142 40.3897 40.6908 39.9967C40.5424 39.8712 40.385 39.7586 40.2275 39.645C39.9558 39.4466 39.6872 39.2445 39.42 39.04C39.1553 38.8373 38.889 38.637 38.62 38.44C38.3087 38.2119 38.001 37.9795 37.6946 37.7448C37.4771 37.5782 37.4771 37.5782 37.2572 37.415C37.0307 37.2494 36.8215 37.0749 36.617 36.8833C36.4895 36.764 36.3587 36.6517 36.2225 36.5425C36.0551 36.4079 35.8967 36.2667 35.74 36.12C35.5519 35.944 35.3588 35.7778 35.1578 35.6164C35.0161 35.5004 34.878 35.3804 34.74 35.26C34.5337 35.0801 34.3248 34.9044 34.1121 34.732C33.9329 34.5849 33.7578 34.4332 33.5821 34.282C33.2916 34.0322 32.9998 33.7841 32.7025 33.5425C32.5327 33.4043 32.366 33.2629 32.2 33.12C32.1293 33.0594 32.0585 32.9988 31.9877 32.9382C31.9437 32.9003 31.8998 32.8623 31.8561 32.8242C31.7978 32.7732 31.7389 32.7228 31.68 32.6725C31.647 32.6439 31.614 32.6153 31.58 32.5858C31.4653 32.5103 31.3756 32.4932 31.24 32.48C31.2771 32.7969 31.3789 33.0004 31.57 33.2525C32.0198 33.8613 32.4274 34.4996 32.8427 35.1321C33.2842 35.8043 33.7292 36.474 34.18 37.14C34.4753 37.5763 34.7697 38.0131 35.0599 38.4528C35.0846 38.4902 35.1093 38.5277 35.1348 38.5662C35.2584 38.7535 35.3811 38.9413 35.5027 39.1299C35.7164 39.4602 35.9311 39.7817 36.18 40.0867C36.7162 40.7578 36.9003 41.3637 36.9975 42.22C37.0034 42.2675 37.0092 42.3151 37.0152 42.364C37.0784 42.9184 37.0856 43.3578 36.7771 43.8383C36.6629 43.9815 36.582 44.0476 36.405 44.0889C36.2963 44.116 36.2169 44.1483 36.1175 44.1973C35.4674 44.4992 34.8616 44.348 34.192 44.165C34.0718 44.1167 34.0319 44.0835 33.9597 43.9786C33.9395 43.9499 33.9193 43.9211 33.8985 43.8916C33.8775 43.8605 33.8566 43.8295 33.835 43.7975C33.8125 43.7653 33.7901 43.733 33.7669 43.6998C33.7202 43.6329 33.6738 43.5657 33.6276 43.4984C33.5457 43.3792 33.4623 43.261 33.3787 43.1429C33.3394 43.0873 33.3001 43.0316 33.2609 42.9759C33.1155 42.7694 32.9682 42.5645 32.82 42.36C32.5906 42.0433 32.3645 41.7244 32.1396 41.4045C32.0794 41.3192 32.0191 41.2339 31.9588 41.1487C31.5044 40.5074 31.065 39.8584 30.6373 39.199C30.5135 39.0083 30.3893 38.8179 30.265 38.6275C30.2405 38.5899 30.216 38.5523 30.1907 38.5135C30.0294 38.2662 29.868 38.0191 29.7064 37.772C29.6489 37.6841 29.5914 37.5962 29.5339 37.5082C29.3965 37.2978 29.2588 37.0876 29.1205 36.8778C28.801 36.3922 28.4943 35.9012 28.2 35.4C28.175 35.3574 28.1499 35.3148 28.1241 35.271C27.952 34.9782 27.7804 34.6852 27.609 34.392C27.4628 34.1421 27.3164 33.8923 27.17 33.6425C27.156 33.6185 27.142 33.5946 27.1275 33.5699C26.8595 33.1126 26.5902 32.6561 26.32 32.2C26.2276 32.2264 26.1352 32.2528 26.04 32.28C26.0406 32.3051 26.0412 32.3302 26.0417 32.3561C26.0456 32.671 26.0215 32.98 25.9917 33.2934C25.9864 33.3503 25.9812 33.4073 25.9759 33.4642C25.9616 33.6196 25.947 33.7749 25.9324 33.9303C25.9166 34.0982 25.9011 34.2662 25.8855 34.4342C25.855 34.7627 25.8243 35.0913 25.7936 35.4198C25.7577 35.8025 25.7221 36.1853 25.6865 36.5681C25.6229 37.251 25.5592 37.9338 25.4954 38.6167C25.4334 39.2792 25.3716 39.9416 25.3099 40.6041C25.3042 40.6654 25.3042 40.6654 25.2984 40.7279C25.2907 40.8101 25.2831 40.8923 25.2754 40.9746C25.2698 41.0355 25.2698 41.0355 25.264 41.0975C25.2602 41.138 25.2565 41.1785 25.2526 41.2202C25.1938 41.8519 25.1348 42.4836 25.0759 43.1153C25.0546 43.3441 25.0332 43.573 25.0119 43.8018C24.9781 44.1647 24.9442 44.5276 24.9103 44.8905C24.8948 45.0563 24.8793 45.2221 24.8638 45.3878C24.8497 45.5396 24.8355 45.6914 24.8213 45.8432C24.8137 45.924 24.8062 46.0049 24.7987 46.0858C24.7863 46.2172 24.7735 46.3486 24.76 46.48C24.7554 46.5256 24.7554 46.5256 24.7508 46.5722C24.7285 46.7611 24.6663 46.844 24.5325 46.98C24.3952 47.1155 24.3952 47.1155 24.2763 47.2662C24.0945 47.5288 23.8005 47.6168 23.5133 47.7255C23.4225 47.76 23.3323 47.7961 23.2421 47.8323C23.184 47.855 23.1259 47.8775 23.0677 47.9C23.041 47.9107 23.0143 47.9214 22.9868 47.9324C22.8029 48.0007 22.6703 48.0054 22.48 47.96C22.3789 47.8942 22.3789 47.8942 22.2825 47.8075C22.1519 47.6927 22.024 47.5869 21.8792 47.4905C21.5083 47.2404 21.3079 47.0343 21.1544 46.6092C21.1149 46.5067 21.0692 46.4084 21.0224 46.3091C20.9882 46.2352 20.9541 46.1614 20.92 46.0875C20.9036 46.0531 20.8871 46.0188 20.8702 45.9834C20.8553 45.9498 20.8404 45.9163 20.825 45.8817C20.8115 45.8524 20.798 45.8232 20.7841 45.793C20.7118 45.4536 20.8385 45.0774 20.913 44.7467C20.9249 44.6929 20.9368 44.6391 20.9486 44.5853C20.9744 44.4682 21.0003 44.3511 21.0263 44.234C21.3616 42.72 21.6696 41.2008 21.9741 39.6803C22.0008 39.5472 22.0274 39.4141 22.0541 39.281C22.0894 39.1049 22.1246 38.9287 22.1598 38.7525C22.2486 38.3085 22.3396 37.865 22.4346 37.4223C22.6905 36.2261 22.8933 35.0312 23.0614 33.8197C23.0993 33.5463 23.138 33.273 23.1778 32.9998C23.1833 32.9625 23.1887 32.9251 23.1943 32.8866C23.305 32.1375 23.4777 31.4092 23.68 30.68C23.4418 30.7737 23.4418 30.7737 23.2685 30.9526C23.2468 30.9828 23.2251 31.013 23.2028 31.0441C23.1804 31.0766 23.1581 31.1091 23.135 31.1425C23.111 31.1763 23.0869 31.2101 23.0621 31.245C23.0121 31.3153 22.9623 31.3857 22.9128 31.4564C22.8237 31.5832 22.7328 31.7087 22.6417 31.8341C22.534 31.9824 22.4271 32.1312 22.32 32.28C22.032 32.6802 21.7423 33.0792 21.4493 33.4758C21.2915 33.6895 21.1353 33.9044 20.98 34.12C20.6127 34.6301 20.2393 35.1356 19.8614 35.6379C19.6359 35.9381 19.4137 36.2406 19.1913 36.5431C19.0511 36.7337 18.9106 36.9242 18.7702 37.1147C18.6525 37.2745 18.5348 37.4343 18.4172 37.5942C18.1801 37.9166 17.942 38.2381 17.7014 38.5579C17.5481 38.7619 17.3963 38.967 17.245 39.1725C17.2217 39.2042 17.1983 39.2358 17.1743 39.2685C17.0545 39.431 16.9353 39.5939 16.8169 39.7575C16.4461 40.2682 16.062 40.749 15.6097 41.1905C15.5025 41.2975 15.4043 41.4104 15.3058 41.5255C15.2183 41.6246 15.1242 41.7162 15.0299 41.8089C14.9574 41.8826 14.8899 41.9591 14.8225 42.0375C14.701 42.1786 14.5716 42.3092 14.4389 42.4398C14.3371 42.5433 14.2432 42.6526 14.1486 42.7626C14.06 42.8626 13.9653 42.9552 13.87 43.0487C13.7967 43.1233 13.7283 43.2008 13.66 43.28C13.5489 43.409 13.4319 43.529 13.3105 43.6483C13.2111 43.7494 13.1191 43.8564 13.0271 43.9642C13.0049 43.9892 12.9828 44.0142 12.96 44.04C12.9382 44.0664 12.9164 44.0928 12.8939 44.12C12.7586 44.2353 12.6099 44.2895 12.445 44.3525C12.3976 44.3719 12.3976 44.3719 12.3492 44.3918C12.3028 44.4098 12.3028 44.4098 12.2553 44.4283C12.2135 44.4447 12.2135 44.4447 12.1708 44.4614C11.9675 44.5031 11.7984 44.4291 11.6192 44.3381C11.5783 44.3175 11.5374 44.2969 11.4953 44.2756C11.4531 44.254 11.411 44.2323 11.3675 44.21C11.3258 44.189 11.2842 44.1679 11.2412 44.1462C10.9978 44.0228 10.7574 43.8947 10.52 43.76C10.5333 43.5835 10.5475 43.4071 10.5625 43.2307C10.5675 43.1708 10.5722 43.1109 10.5767 43.051C10.6247 42.4131 10.6247 42.4131 10.8 42.16C10.8148 42.1376 10.8295 42.1151 10.8447 42.092C10.8907 42.0242 10.9387 41.9583 10.9875 41.8925C11.0232 41.8436 11.0589 41.7946 11.0946 41.7456C11.1123 41.7213 11.1301 41.6969 11.1484 41.6718C11.2262 41.5636 11.3004 41.4529 11.375 41.3425C11.5247 41.1253 11.685 40.9194 11.8512 40.7146C12.0036 40.5265 12.1514 40.3354 12.2975 40.1425C12.4912 39.8873 12.6904 39.6374 12.8934 39.3896C13.0259 39.2276 13.1556 39.0639 13.2825 38.8975C13.4233 38.7128 13.5668 38.5308 13.7125 38.35C13.8843 38.1367 14.052 37.9207 14.2175 37.7025C14.4112 37.4474 14.6103 37.1975 14.8133 36.9497C14.9802 36.7455 15.1418 36.5379 15.3017 36.3283C15.4058 36.1921 15.5115 36.0572 15.6175 35.9225C15.6359 35.8991 15.6543 35.8758 15.6732 35.8517C15.7645 35.7358 15.8559 35.6199 15.9474 35.5041C16.2128 35.1675 16.4739 34.8278 16.7337 34.4869C16.8423 34.3445 16.9512 34.2023 17.06 34.06C17.1034 34.0033 17.1467 33.9467 17.19 33.89C17.45 33.55 17.71 33.21 17.97 32.87C17.9915 32.8419 18.013 32.8138 18.0351 32.7849C18.0782 32.7285 18.1214 32.672 18.1646 32.6156C18.2755 32.4706 18.3864 32.3254 18.4971 32.1801C18.8477 31.7201 19.2053 31.2706 19.5855 30.8345C19.7119 30.6895 19.8374 30.5437 19.9625 30.3975C19.982 30.3748 20.0014 30.3521 20.0214 30.3287C20.0608 30.2827 20.1001 30.2367 20.1394 30.1908C20.2028 30.1167 20.2663 30.0428 20.3297 29.9689C20.3511 29.944 20.3725 29.919 20.3945 29.8934C20.4351 29.8461 20.4756 29.7989 20.5162 29.7517C20.5531 29.7088 20.5898 29.6657 20.6264 29.6226C20.6628 29.5801 20.6997 29.5381 20.7372 29.4966C20.8387 29.3817 20.9045 29.3004 20.9125 29.145C20.9162 29.0732 20.9162 29.0732 20.92 29C20.6382 29.1585 20.3662 29.3266 20.0983 29.5076C19.8582 29.6693 19.6143 29.8247 19.37 29.98C19.0027 30.2138 18.6392 30.4525 18.2775 30.695C17.9735 30.8988 17.6687 31.1005 17.3569 31.2925C17.005 31.5098 16.6604 31.7387 16.3144 31.9652C16.1407 32.0788 15.9666 32.1919 15.7925 32.305C15.7271 32.3475 15.6617 32.39 15.5963 32.4325C15.5641 32.4534 15.5318 32.4744 15.4986 32.4959C15.3978 32.5614 15.297 32.627 15.1963 32.6925C14.8568 32.9133 14.5171 33.1338 14.1771 33.3536C13.8583 33.5598 13.5402 33.7668 13.222 33.9737C13.0214 34.1041 12.8209 34.2344 12.6203 34.3648C12.4521 34.4741 12.284 34.5834 12.1158 34.6928C11.7765 34.9135 11.437 35.1339 11.0971 35.3536C10.7443 35.5818 10.3922 35.8109 10.04 36.04C9.65017 36.2936 9.26031 36.5471 8.87003 36.8C8.84336 36.8173 8.81669 36.8346 8.78922 36.8524C8.7379 36.8857 8.68634 36.9185 8.63456 36.951C8.55982 36.9984 8.55982 36.9984 8.49425 37.0531C8.21916 37.2606 7.87136 37.2682 7.54003 37.3075C7.47752 37.3155 7.41502 37.3235 7.35253 37.3316C7.22899 37.3475 7.10541 37.3629 6.98175 37.3779C6.86093 37.3924 6.74022 37.4077 6.61956 37.4235C6.59078 37.4273 6.562 37.431 6.53235 37.4349C6.45404 37.4451 6.37574 37.4554 6.29745 37.4657C5.95099 37.5017 5.95099 37.5017 5.84659 37.4286C5.81612 37.3934 5.81612 37.3934 5.78503 37.3575C5.68854 37.2519 5.59161 37.1551 5.48253 37.0625C5.45618 37.0391 5.42983 37.0157 5.40268 36.9916C5.34629 36.9427 5.28813 36.8959 5.22862 36.8509C5.09811 36.7427 5.01241 36.6596 4.97721 36.4903C4.97516 36.3704 4.98205 36.2541 4.99337 36.1349C5.00123 36.0229 5.0009 35.9119 5.00081 35.7997C5.01177 35.0646 5.01177 35.0646 5.22692 34.8154C5.34311 34.7042 5.46801 34.6115 5.60003 34.52C5.64969 34.4843 5.64969 34.4843 5.70036 34.448C5.92208 34.2895 6.14773 34.1378 6.37577 33.9885C6.51776 33.8952 6.65778 33.7991 6.79753 33.7025C6.8349 33.6768 6.8349 33.6768 6.87303 33.6506C7.10437 33.4913 7.33463 33.3306 7.56503 33.17C7.91475 32.9262 8.26599 32.6848 8.61872 32.4454C8.65241 32.4225 8.6861 32.3997 8.72081 32.3761C8.75359 32.3538 8.78638 32.3316 8.82015 32.3086C8.95684 32.2147 9.09183 32.1184 9.22659 32.0217C9.36142 31.9326 9.50467 31.866 9.65003 31.7959C9.84684 31.6985 10.0367 31.5885 10.2275 31.48C10.267 31.4576 10.3065 31.4351 10.3471 31.412C10.4296 31.3651 10.512 31.3182 10.5945 31.2712C10.8009 31.1541 11.0079 31.0383 11.215 30.9225C11.2565 30.8993 11.2981 30.876 11.3408 30.8521C11.4251 30.8049 11.5094 30.7578 11.5937 30.7106C11.8025 30.5938 12.0113 30.4769 12.22 30.36C12.4709 30.2195 12.7218 30.079 12.9728 29.9386C13.3757 29.7132 13.7783 29.4874 14.18 29.26C14.6238 29.0088 15.0687 28.7596 15.5137 28.5106C15.7225 28.3938 15.9313 28.2769 16.14 28.16C16.3909 28.0195 16.6418 27.879 16.8928 27.7386C17.1003 27.6225 17.3078 27.5063 17.5151 27.39C17.6003 27.3423 17.6855 27.2945 17.7707 27.2468C17.8981 27.1755 18.0253 27.104 18.1525 27.0325C18.1912 27.0109 18.2299 26.9892 18.2697 26.9669C18.3056 26.9468 18.3414 26.9266 18.3783 26.9058C18.4096 26.8882 18.441 26.8706 18.4733 26.8525C18.6455 26.7483 18.7375 26.6815 18.7975 26.485C18.8086 26.4513 18.8197 26.4175 18.8311 26.3828C18.8465 26.2785 18.8465 26.2785 18.8088 26.1763C18.6835 26.0404 18.5821 26.0505 18.4024 26.041C18.3582 26.0396 18.3139 26.0382 18.2683 26.0368C18.2216 26.0347 18.1749 26.0325 18.1268 26.0303C17.998 26.0245 17.8693 26.0196 17.7404 26.0151C17.6309 26.0112 17.5213 26.0065 17.4118 26.0018C17.0346 25.9857 16.6573 25.9724 16.28 25.96C16.2523 25.9591 16.2247 25.9582 16.1961 25.9572C15.5101 25.9346 14.8239 25.9159 14.1377 25.8984C13.4691 25.8814 12.8006 25.863 12.1321 25.8439C12.1013 25.843 12.0705 25.8421 12.0387 25.8412C10.0963 25.7856 8.15519 25.7012 6.21429 25.6065C6.07853 25.5999 5.94276 25.5934 5.807 25.5868C5.2376 25.5592 4.66825 25.5307 4.09895 25.5012C3.97943 25.4951 3.85991 25.4891 3.74039 25.4831C3.5552 25.4738 3.37005 25.464 3.1849 25.4539C3.11832 25.4504 3.05174 25.4469 2.98515 25.4436C2.55696 25.4224 2.14223 25.3712 1.72052 25.2941C1.63891 25.2798 1.5572 25.2675 1.47519 25.2557C1.19109 25.2137 1.0234 25.1559 0.847995 24.9253C0.804451 24.8633 0.804451 24.8633 0.760027 24.8C0.714604 24.7389 0.669016 24.678 0.623308 24.6172C0.502089 24.4551 0.382146 24.2922 0.263806 24.128C0.222804 24.0714 0.181287 24.0152 0.139138 23.9595C0.00383298 23.7786 -0.0262119 23.6607 2.65873e-05 23.44C0.00105784 23.4047 0.00208909 23.3695 0.00315159 23.3331C0.0178118 23.1562 0.039622 23.0411 0.166433 22.912C0.280979 22.8281 0.39905 22.7543 0.520027 22.68C0.560194 22.65 0.600361 22.6201 0.641745 22.5892C0.701532 22.545 0.701532 22.545 0.762527 22.5C0.798466 22.4732 0.834405 22.4464 0.871433 22.4187C1.17723 22.279 1.58529 22.37 1.91231 22.3959C1.98932 22.4018 2.06634 22.4077 2.14335 22.4136C2.22604 22.4199 2.30872 22.4263 2.3914 22.4327C2.69306 22.4561 2.99477 22.4788 3.29647 22.5015C3.32821 22.5039 3.35995 22.5063 3.39265 22.5088C4.58975 22.599 5.78755 22.6796 6.98526 22.7613C7.22604 22.7778 7.4668 22.7943 7.70757 22.8109C8.08315 22.8368 8.45875 22.8626 8.83436 22.8882C8.90207 22.8928 8.96979 22.8975 9.0375 22.9021C9.77526 22.9525 10.513 23.001 11.2514 23.0404C11.3106 23.0436 11.3698 23.0467 11.4289 23.0499C11.458 23.0515 11.4871 23.053 11.517 23.0546C12.8648 23.1268 14.2019 23.2422 15.5436 23.3935C15.9399 23.4382 16.3362 23.4822 16.7326 23.5252C16.8672 23.54 17.0017 23.5554 17.1362 23.5709C17.7192 23.6359 18.2936 23.6522 18.88 23.64C18.9064 23.5476 18.9328 23.4552 18.96 23.36C18.9257 23.3445 18.9257 23.3445 18.8907 23.3286C18.7918 23.2756 18.7151 23.2139 18.6292 23.1417C18.3562 22.9201 18.0646 22.7279 17.7725 22.5325C17.7121 22.4919 17.6518 22.4513 17.5914 22.4106C17.4678 22.3274 17.3441 22.2443 17.2203 22.1612C16.976 21.9971 16.7323 21.8319 16.4887 21.6667C16.3861 21.5971 16.2835 21.5276 16.1809 21.4581C15.9807 21.3225 15.7805 21.1868 15.5804 21.0511C15.2404 20.8205 14.9003 20.5902 14.56 20.36C14.5261 20.3371 14.4922 20.3141 14.4573 20.2905C14.2311 20.1374 14.0048 19.9845 13.7785 19.8316C13.7041 19.7814 13.6298 19.7312 13.5555 19.6809C13.1793 19.4265 12.8024 19.1738 12.4205 18.9281C12.0796 18.7087 11.7413 18.4852 11.4029 18.262C11.2907 18.188 11.1785 18.114 11.0663 18.0401C10.6241 17.7486 10.183 17.4557 9.74255 17.1617C9.6018 17.0678 9.46082 16.9742 9.31956 16.8811C8.87121 16.5852 8.43256 16.2785 8.00003 15.96C7.96921 15.9373 7.93838 15.9146 7.90663 15.8912C7.77913 15.7973 7.65168 15.7034 7.52431 15.6093C7.25032 15.4071 6.9746 15.2073 6.69815 15.0084C6.57986 14.9233 6.46198 14.8377 6.3444 14.7516C5.98696 14.4904 5.62453 14.2392 5.25373 13.9973C4.89855 13.7641 4.58962 13.5347 4.32753 13.1975C4.30569 13.1702 4.28385 13.143 4.26135 13.1149C4.19972 13.0373 4.13887 12.9597 4.08003 12.88C4.05281 12.8457 4.02558 12.8113 3.99754 12.7759C3.89572 12.5974 3.87174 12.4329 3.84315 12.2312C3.83716 12.1922 3.83117 12.1532 3.825 12.1129C3.8125 12.0307 3.80028 11.9484 3.78833 11.866C3.77007 11.7403 3.75099 11.6148 3.73175 11.4892C3.71967 11.4091 3.70763 11.3289 3.69565 11.2487C3.69003 11.2114 3.68441 11.174 3.67861 11.1355C3.60768 10.6442 3.60768 10.6442 3.72362 10.4856C3.76101 10.4465 3.79902 10.408 3.83753 10.37C3.87833 10.3277 3.91911 10.2854 3.95987 10.2431C3.98028 10.2223 4.0007 10.2015 4.02173 10.1801C4.10369 10.0956 4.18185 10.008 4.26003 9.91999C4.39914 9.765 4.53929 9.61101 4.68003 9.45749C4.69929 9.43648 4.71854 9.41547 4.73839 9.39382C4.91613 9.20018 4.91613 9.20018 5.02534 9.19503C5.37778 9.19651 5.72819 9.20823 6.08003 9.22999C6.13004 9.23247 6.18006 9.23494 6.23159 9.23749C6.78178 9.27167 7.20362 9.37123 7.61595 9.74437C7.74373 9.85529 7.88008 9.95551 8.01503 10.0575C8.07415 10.1025 8.13327 10.1475 8.19237 10.1925C8.22093 10.2142 8.24949 10.2359 8.27891 10.2583C8.63255 10.5273 8.98332 10.8001 9.33392 11.0731C9.50888 11.2093 9.68417 11.345 9.86003 11.48C10.0988 11.6633 10.3364 11.8482 10.5739 12.0331C10.7489 12.1693 10.9242 12.305 11.1 12.44C11.3074 12.5992 11.5138 12.7594 11.72 12.92C11.8122 12.9917 11.9043 13.0633 11.9965 13.135C12.0479 13.175 12.0993 13.215 12.1506 13.255C12.5159 13.5393 12.888 13.8137 13.2612 14.0874C13.4209 14.2047 13.5805 14.3224 13.74 14.44C13.8034 14.4867 13.8667 14.5333 13.93 14.58C14.5634 15.0467 15.1967 15.5133 15.83 15.98C15.8614 16.0031 15.8927 16.0262 15.9251 16.05C15.9883 16.0966 16.0515 16.1432 16.1148 16.1898C16.2745 16.3076 16.4343 16.4252 16.5942 16.5428C16.7529 16.6595 16.9114 16.7764 17.0698 16.8934C17.1287 16.9368 17.1875 16.9802 17.2465 17.0234C17.6323 17.3067 18.0009 17.6035 18.36 17.92C18.4598 17.8718 18.5473 17.8218 18.64 17.76C18.6221 17.5222 18.517 17.3573 18.3975 17.1575C18.3534 17.0821 18.3094 17.0066 18.2655 16.9311C18.2419 16.8906 18.2183 16.8501 18.194 16.8084C18.0628 16.5807 17.9365 16.3503 17.81 16.12C17.7828 16.0705 17.7556 16.0211 17.7284 15.9716C17.6732 15.8713 17.618 15.7709 17.5628 15.6705C17.4279 15.4252 17.2927 15.1801 17.1575 14.935C17.1443 14.911 17.131 14.8869 17.1174 14.8622C16.901 14.4699 16.6838 14.0782 16.4659 13.6868C16.3366 13.4543 16.2078 13.2216 16.0799 12.9883C15.7642 12.4128 15.4419 11.842 15.1086 11.2764C14.9927 11.0796 14.8776 10.8823 14.7625 10.685C14.728 10.6259 14.728 10.6259 14.6928 10.5656C14.4955 10.2272 14.2998 9.88788 14.1043 9.54845C13.9143 9.21885 13.723 8.89005 13.5308 8.56173C13.4099 8.35496 13.2899 8.14774 13.1713 7.93968C12.9353 7.52627 12.6968 7.11591 12.44 6.71499C12.4174 6.6796 12.3948 6.6442 12.3715 6.60774C12.2492 6.41698 12.1252 6.22745 12 6.03859C11.7083 5.59335 11.4711 5.1813 11.3449 4.6623C11.3169 4.547 11.2864 4.43246 11.2558 4.3178C11.0389 3.49292 11.0389 3.49292 11 3.07999C10.993 3.03317 10.986 2.98635 10.9788 2.93812C10.9447 2.56436 11.0421 2.3698 11.2697 2.09067C11.3156 2.03582 11.3618 1.98116 11.4082 1.92667C11.5342 1.77471 11.6488 1.61444 11.765 1.45499C11.801 1.40704 11.801 1.40704 11.8376 1.35812C11.9447 1.21465 12.0471 1.07254 12.1363 0.91718C12.3576 0.541827 12.6132 0.403761 13.0283 0.277649C13.277 0.221366 13.5091 0.249017 13.76 0.279992Z"
        fill={props.color || "#D97656"}
      />
    </svg>
  );
};

export const CloudflareLogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M36.4334 22.1577L12.7125 22.3201V34.3629H43.2308V25.9081L37.441 22.5828L36.4334 22.1577Z"
        fill="white"
      />
      <path
        d="M32.6683 33.2464C32.9517 32.2733 32.8437 31.3815 32.371 30.7187C31.9389 30.1104 31.2087 29.7682 30.3299 29.7189L13.6916 29.5029C13.5835 29.5029 13.4885 29.4484 13.4349 29.3681C13.3813 29.2877 13.3675 29.178 13.3943 29.0708C13.4246 28.989 13.4776 28.9176 13.5471 28.865C13.6166 28.8123 13.6997 28.7806 13.7866 28.7735L30.5736 28.5575C32.5611 28.4711 34.7232 26.8551 35.4802 24.8814L36.4308 22.3806C36.473 22.2779 36.4824 22.1647 36.4575 22.0565C35.3774 17.1637 31.0108 13.5144 25.794 13.5144C23.5094 13.5144 21.2822 14.2301 19.4253 15.5611C17.5684 16.892 16.1752 18.7713 15.4415 20.9348C14.4952 20.2323 13.2923 19.8547 11.9952 19.9843C10.87 20.1007 9.81918 20.6009 9.01929 21.4008C8.21939 22.2007 7.7192 23.2515 7.60276 24.3767C7.54674 24.9516 7.58764 25.5317 7.72374 26.0929C5.90538 26.1443 4.17884 26.9032 2.91157 28.2083C1.64431 29.5133 0.936375 31.2614 0.938451 33.0805C0.940717 33.4194 0.963225 33.7579 1.00585 34.0941C1.03178 34.2566 1.16831 34.3775 1.32991 34.3775H32.0374C32.2128 34.3775 32.3831 34.2566 32.4297 34.0803L32.6596 33.2559L32.6683 33.2464Z"
        fill={props.color || "#F38020"}
      />
      <path
        d="M37.9663 22.5551L37.5066 22.569C37.3986 22.569 37.3036 22.6502 37.2629 22.7591L36.6148 25.0162C36.3314 25.9892 36.4394 26.8811 36.9121 27.5439C37.3442 28.1522 38.0744 28.4944 38.9532 28.5437L42.4945 28.7597C42.6025 28.7597 42.6976 28.8142 42.7511 28.8945C42.7777 28.9387 42.795 28.9879 42.802 29.039C42.809 29.0901 42.8055 29.1421 42.7918 29.1918C42.7614 29.2735 42.7084 29.3449 42.6389 29.3976C42.5694 29.4503 42.4863 29.482 42.3994 29.4891L38.7095 29.7051C36.709 29.7915 34.5599 31.4075 33.8029 33.3812L33.5324 34.0708C33.478 34.2056 33.573 34.3412 33.7225 34.3412H46.3996C46.5482 34.3412 46.683 34.2548 46.7236 34.0976C46.9465 33.2964 47.0601 32.4689 47.0615 31.6373C47.0549 29.2281 46.094 26.9197 44.3892 25.2174C42.6845 23.515 40.3747 22.5575 37.9655 22.5543"
        fill={props.color || "#FAAE40"}
      />
    </svg>
  );
};

export const CrewAILogo = ({
  size = 24,
  ...props
}: SVGProps<SVGSVGElement> & { size?: number }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 48 48" {...props}>
      <path
        d="M12.6415 45.6087C7.74174 43.287 4.86406 39.4286 3.75784 34.2259C2.95197 30.4357 3.31819 26.6839 4.48009 23.024C6.47883 16.728 9.8415 11.2506 14.6785 6.73423C17.2903 4.29556 20.2455 2.38015 23.7261 1.36029C28.2405 0.0375315 32.2769 0.998559 35.9163 3.86041C37.9228 5.43822 39.6561 7.2571 40.6299 9.67739C41.5124 11.8705 41.493 14.1191 41.0054 16.391C40.6436 18.0767 39.98 19.6594 39.316 21.2073C39.4187 21.4459 39.6161 21.4243 39.7621 21.4441C42.4466 21.8066 44.2133 24.1187 44.576 26.8818C45.0388 30.4068 43.8317 33.4441 41.7719 36.1934C37.586 41.7804 32.0078 45.2228 25.2146 46.6924C20.9437 47.6163 16.7415 47.3852 12.6415 45.6087ZM32.8639 25.0805C33.5775 23.1788 34.4659 21.3569 35.3642 19.5378C35.9876 18.2755 36.5277 16.9741 36.8531 15.5978C37.35 13.4964 37.2477 11.5063 35.8209 9.72743C34.4645 8.03623 32.8584 6.67282 30.8975 5.73395C29.4299 5.03128 27.8951 4.84329 26.3016 5.12175C22.8701 5.72139 20.0771 7.54512 17.5719 9.8321C14.2544 12.8606 11.8064 16.5345 9.92337 20.5943C8.14858 24.4207 7.19357 28.4073 7.79913 32.6423C8.27314 35.9574 9.7346 38.7429 12.558 40.6771C15.9755 43.0183 19.7879 43.3611 23.737 42.6863C27.367 42.066 30.6368 40.5428 33.6068 38.38C36.1511 36.5271 38.3062 34.3154 39.6811 31.445C40.3755 29.9952 40.6546 28.4573 40.2243 26.8607C40.0813 26.33 39.8601 25.8088 39.2413 25.6937C38.6111 25.5765 38.2004 25.9965 37.8089 26.4041C37.7099 26.5072 37.6398 26.6387 37.5599 26.7596C36.459 28.4248 35.1745 29.9257 33.6876 31.2656C30.201 34.4073 26.0977 35.8769 21.437 35.8618C19.2325 35.8547 17.7959 34.7886 17.296 32.6582C16.6762 30.0168 16.8785 27.3686 17.8437 24.8567C19.5922 20.3067 22.2188 16.277 25.5187 12.6931C26.3589 11.7805 27.2417 10.8967 28.3681 10.3229C28.709 10.1492 29.0769 9.89825 29.4625 10.2202C29.8744 10.5642 29.8415 11.0176 29.708 11.4759C29.5099 12.1557 29.1594 12.7695 28.8646 13.4079C27.9963 15.2887 27.2058 17.1985 26.7696 19.2353C26.1529 22.115 27.8256 25.0926 30.6162 26.0802C31.724 26.4723 32.2927 26.2368 32.8639 25.0805Z"
        fill={props.color || "#461916"}
      />
    </svg>
  );
};
