import { useCallback } from "react";
import { isEmpty, safeDeserializeUnknown } from "#/utils/object";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useSessionToken } from "#/utils/auth/session-token";
import { apiFetchBtqlObject } from "#/utils/btapi/fetch";
import { useOrg } from "#/utils/user";
import { parseObjectJSON } from "#/utils/schema";
import { type Table, type TypeMap } from "apache-arrow";
import {
  type DataObjectSearch,
  type DataObjectType,
  getIdAndParams,
} from "#/utils/btapi/btapi";
import { type RowData } from "#/ui/arrow-table";
import {
  GRID_EXPECTED_COLUMN_ID,
  GRID_INPUT_COLUMN_ID,
  GRID_METADATA_COLUMN_ID,
} from "#/ui/table/formatters/grid-layout-columns";
import { TransactionIdField } from "./duckdb";
import { z } from "zod";
import { TABLE_BLACKLIST } from "#/ui/summary-paginated-object-viewer";
import { useBtqlQueryBuilder } from "./btql/use-query-builder";
import { type Expr } from "@braintrust/btql/parser";
import { type CustomColumnDefinition } from "./custom-columns/use-custom-columns";
import { type GetRowsForExportFn } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(queries)/table-queries";
import { fetchBtqlPaginated, useFetchBtqlOptions } from "./btql/btql";

const rowSchema = z.record(z.unknown());

export function useGetRowsForExport({
  objectType,
  objectId,
  filters,
  exportedColumns,
  shouldRefetchObject,
  vizQueryRef,
  rowData,
  dataObjectSearchParams,
  layout,
}: {
  objectType: DataObjectType;
  objectId: string | null;
  filters: DataObjectSearch["filters"];
  exportedColumns: string[];
  shouldRefetchObject: boolean;
  vizQueryRef?: React.RefObject<{
    data: Table<TypeMap> | null;
  } | null>;
  rowData?: RowData[] | null;
  dataObjectSearchParams?: Partial<DataObjectSearch>;
  customColumns?: CustomColumnDefinition[];
  layout: string | null;
}): GetRowsForExportFn {
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const { flags } = useFeatureFlags();
  const { columnstoreSearch, disableColumnstoreSearch, btqlMatchFilters } =
    flags;
  const builder = useBtqlQueryBuilder({});
  const btqlOptions = useFetchBtqlOptions();

  return useCallback(
    async (args) => {
      const { allColumns, rowIds } = args ?? {};
      async function queryRows() {
        if (!shouldRefetchObject && !rowIds) {
          if (vizQueryRef === undefined) {
            if (!rowData || rowData.length === 0) {
              throw new Error("No table data loaded");
            }
            return allColumns
              ? rowData
              : rowData.map((r) => {
                  return Object.fromEntries(
                    exportedColumns.map((path) => [path, r[path]]),
                  );
                });
          }
          const vizData = vizQueryRef?.current?.data;
          if (!vizData) {
            throw new Error("No table data loaded");
          }
          const jsonRows = vizData.toArray()?.map((e) => e.toJSON()) || [];
          return jsonRows.map((r) => {
            const filtered = Object.fromEntries(
              exportedColumns.map((path) => [path, r[path]]),
            );
            return parseObjectJSON(objectType, filtered);
          });
        }

        // Refetch the entire object from the API using a BTQL query with no limit.
        if (!objectId) {
          throw new Error("No object ID available");
        }
        const sessionToken = await getOrRefreshToken();
        const dataObjectSearch: DataObjectSearch = {
          id: objectId,
          filters,
          ...dataObjectSearchParams,
        };
        const { id, audit_log, params } = getIdAndParams(dataObjectSearch);

        if (!rowIds) {
          const result = await fetchBtqlPaginated(
            {
              args: {
                query: {
                  select:
                    params?.shape === "summary" ||
                    allColumns ||
                    !exportedColumns?.length
                      ? [{ op: "star" }]
                      : exportedColumns.map((p) => ({
                          expr: {
                            op: "ident",
                            name: [p],
                          },
                          alias: p,
                        })),
                  from: builder.from(objectType, [id], params?.shape),
                  filter: builder.and(...(params?.filters?.btql ?? [])),
                  sort:
                    params?.shape === "summary"
                      ? [{ expr: { btql: "_pagination_key" }, dir: "desc" }]
                      : undefined,
                  limit: 1,
                  custom_columns: params?.custom_columns,
                  preview_length: params?.shape === "summary" ? -1 : undefined,
                },
                brainstoreRealtime: true,
                useColumnstore:
                  columnstoreSearch &&
                  !isEmpty(params?.filters?.btql) &&
                  params.filters.btql.length > 0
                    ? true
                    : disableColumnstoreSearch
                      ? false
                      : undefined,
              },
              ...btqlOptions,
              schema: rowSchema,
            },
            100,
            10,
            false,
          );
          return result.data;
        }

        const versionedResponse = await apiFetchBtqlObject({
          apiUrl: org.api_url,
          objectType,
          objectId: id,
          audit_log,
          sessionToken,
          use_columnstore:
            columnstoreSearch &&
            !isEmpty(params?.filters?.btql) &&
            params.filters.btql.length > 0
              ? true
              : disableColumnstoreSearch
                ? false
                : undefined,
          fmt: "json",
          use_match_search_index: btqlMatchFilters,
          filters: [
            ...(params?.filters?.btql ?? []),
            rowIds
              ? builder.or(
                  ...rowIds.map(
                    (id): Expr => ({
                      op: "eq",
                      left: builder.ident("id"),
                      right: {
                        op: "literal",
                        value: id,
                      },
                    }),
                  ),
                )
              : undefined,
          ].filter((v) => !!v),
          limit: params?.limit,
          sort:
            params?.shape === "summary"
              ? [{ expr: { btql: "_pagination_key" }, dir: "desc" }]
              : undefined,
          select:
            params?.shape === "summary" || allColumns
              ? [{ op: "star" }]
              : exportedColumns.map((p) => ({
                  expr: {
                    op: "ident",
                    name: [p],
                  },
                  alias: p,
                })),
          custom_columns: params?.custom_columns,
          shape: params?.shape,
          preview_length: params?.shape === "summary" ? -1 : undefined,
        });
        if (versionedResponse.response.status !== 200) {
          throw new Error(await versionedResponse.response.text());
        }
        const { data } = await versionedResponse.response.json();

        return data.flatMap((r: Record<string, unknown>) => {
          if (rowIds && !rowIds.includes(`${r.id}`)) {
            return [];
          }
          if (allColumns) {
            return [r];
          }
          return Object.fromEntries(
            exportedColumns.map((col) => {
              return [col, r[col]];
            }),
          );
        });
      }
      const rows = await queryRows();
      if (!rows) {
        return;
      }
      const filteredData = filterDataByColumnVisibility({
        data: rows,
        layout,
        columnVisibility: args?.columnVisibility,
      });

      return filteredData;
    },
    [
      shouldRefetchObject,
      objectId,
      getOrRefreshToken,
      filters,
      org.api_url,
      objectType,
      columnstoreSearch,
      disableColumnstoreSearch,
      btqlMatchFilters,
      exportedColumns,
      vizQueryRef,
      rowData,
      dataObjectSearchParams,
      layout,
      builder,
      btqlOptions,
    ],
  );
}

export function filterDataByColumnVisibility({
  data,
  layout,
  isPlayground,
  columnVisibility,
}: {
  data: Record<string, unknown>[];
  layout: string | null;
  isPlayground?: boolean;
  columnVisibility?: Record<string, boolean>;
}) {
  if (!columnVisibility) {
    return data;
  }

  const isRecord = (value: unknown): value is Record<string, unknown> => {
    return value !== null && typeof value === "object" && !Array.isArray(value);
  };

  const filterNestedObject = (
    obj: Record<string, unknown>,
    prefix: string,
  ): Record<string, unknown> => {
    const filtered: Record<string, unknown> = {};

    Object.entries(obj).forEach(([key, value]) => {
      const fullKey = `${prefix}.${key}`;

      if (columnVisibility?.[fullKey] !== false) {
        filtered[key] = value;
      }
    });

    return filtered;
  };

  const isGridLayout = layout === "grid";
  const isInputVisible =
    columnVisibility?.[isGridLayout ? GRID_INPUT_COLUMN_ID : "input"] !== false;
  return data.flatMap((row) => {
    const filteredRow: Record<string, unknown> = {};

    Object.entries(row).forEach(([key, value]) => {
      if (TABLE_BLACKLIST.includes(key) || key === TransactionIdField) {
        return;
      }
      if (layout === "grid" && key === "id") {
        return;
      }
      switch (key) {
        case "metrics":
          if (isGridLayout) {
            break;
          }
        // passthrough
        case "scores": {
          if (isRecord(value)) {
            const filteredValue = filterNestedObject(value, key);
            const numKeys = Object.keys(filteredValue).length;
            if (
              numKeys > 1 ||
              (numKeys === 1 && !("cached" in filteredValue))
            ) {
              filteredRow[key] = filteredValue;
            }
          }
          break;
        }
        case "span_type_info":
          if (
            columnVisibility?.["span_type_info"] !== false &&
            (!isGridLayout || isInputVisible)
          ) {
            filteredRow["name"] = parseSpanTypeInfoName(value);
          }
          break;
        case "input":
          if (isInputVisible) {
            filteredRow["input"] = value;
          }
          break;
        case "expected":
          if (
            columnVisibility?.[
              isGridLayout && isPlayground ? GRID_EXPECTED_COLUMN_ID : key
            ] !== false
          ) {
            filteredRow["expected"] = value;
          }
          break;
        case "metadata":
          if (
            columnVisibility?.[
              isGridLayout && isPlayground ? GRID_METADATA_COLUMN_ID : key
            ] !== false
          ) {
            filteredRow["metadata"] = value;
          }
          break;
        case "error":
          if (columnVisibility?.["output"] !== false && value) {
            filteredRow["error"] = value;
          }
          break;
        case "created":
          if (isGridLayout) {
            break;
          }
        // passthrough to default
        default: {
          if (columnVisibility?.[key] !== false) {
            // For non-score/metric fields, keep them if not explicitly hidden
            filteredRow[key] = value;
          }
          break;
        }
      }
    });

    return Object.keys(filteredRow).length > 0 ? [filteredRow] : [];
  });
}

const spanTypeInfoSchema = z.object({
  name: z.string(),
});

function parseSpanTypeInfoName(value: unknown) {
  let parsedValue;
  try {
    parsedValue = spanTypeInfoSchema.parse(safeDeserializeUnknown(value));
  } catch (e) {
    return null;
  }

  return parsedValue.name;
}
