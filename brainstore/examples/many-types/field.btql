select: * | filter: id;

/*!optimizer -- Make sure that the search pushes down an equality on the bool field

  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.search.TermQuery | .field.name == "bool" and .value == true

*/
select: * | filter: bool;

select: * | filter: int;
select: * | filter: float;
select: * | filter: str;
select: * | filter: unknown;
select: * | filter: arr;
select: * | filter: obj;

unpivot: obj as (key, value) | select: key, value | filter: obj is not null;
unpivot: obj as (key, value) | measures: sum(value) | filter: obj is not null;
unpivot: obj as (key, value) | measures: avg(value) | filter: obj is not null and obj.flag is null;

measures: sum(obj.float = 3 ? float : null);
measures: sum(obj.flag = 1 ? 1 : null);
measures: sum(obj.flag = "hello" ? 1 : null);

-- error, we don't support filtering unpivot fields
unpivot: obj as (key, value) | select: key, value | filter: key is not null;
